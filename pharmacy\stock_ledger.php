<?php
require_once '../database.php';

// Create table if not exists
$sql = "CREATE TABLE IF NOT EXISTS pharmacy_stock_ledger (
    id INT AUTO_INCREMENT PRIMARY KEY,
    item_no VARCHAR(50),
    itemid INT(10),  -- Changed to match the items table's itemid type
    lot_no VARCHAR(100),
    expiry_date DATE,
    unit_cost DECIMAL(10,2),
    selling_price DECIMAL(10,2),
    qty_received INT,
    cris_no VARCHAR(50),
    beg_balance VARCHAR(50),
    date_received DATE,
    ics_no VARCHAR(50),
    supplier VARCHAR(100),
    date_encoded TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT fk_itemid FOREIGN KEY (itemid) REFERENCES items(itemid)
)";

try {
    $conn->exec($sql);
} catch(PDOException $e) {
    die("Error creating table: " . $e->getMessage());
}

// Drop item_no column if it exists (since we're using itemid directly now)
try {
    $conn->exec("ALTER TABLE pharmacy_stock_ledger DROP COLUMN item_no");
} catch (PDOException $e) {
    // Column might not exist or already dropped, ignore error
}

// INSERT
if (isset($_POST['submit'])) {
    try {
        $sql = "INSERT INTO pharmacy_stock_ledger (itemid, lot_no, expiry_date, unit_cost, selling_price, qty_received, cris_no, beg_balance, date_received, ics_no, supplier)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_POST['itemid'],
            $_POST['lot_no'],
            $_POST['expiry_date'],
            $_POST['unit_cost'],
            $_POST['selling_price'],
            $_POST['qty_received'],
            $_POST['cris_no'],
            $_POST['beg_balance'],
            $_POST['date_received'],
            $_POST['ics_no'],
            $_POST['supplier']
        ]);
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch (Exception $e) {
        die("Error: " . $e->getMessage());
    }
}

// UPDATE
if (isset($_POST['update'])) {
    try {
        $sql = "UPDATE pharmacy_stock_ledger SET
                itemid = ?,
                lot_no = ?,
                expiry_date = ?,
                unit_cost = ?,
                selling_price = ?,
                qty_received = ?,
                cris_no = ?,
                beg_balance = ?,
                date_received = ?,
                ics_no = ?,
                supplier = ?
                WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_POST['itemid'],
            $_POST['lot_no'],
            $_POST['expiry_date'],
            $_POST['unit_cost'],
            $_POST['selling_price'],
            $_POST['qty_received'],
            $_POST['cris_no'],
            $_POST['beg_balance'],
            $_POST['date_received'],
            $_POST['ics_no'],
            $_POST['supplier'],
            $_POST['id']
        ]);
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch (Exception $e) {
        die("Error: " . $e->getMessage());
    }
}

// DELETE
if (isset($_POST['delete'])) {
    $sql = "DELETE FROM pharmacy_stock_ledger WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$_POST['id']]);
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// SEARCH
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sql = "SELECT sl.*, i.generaldescription
        FROM pharmacy_stock_ledger sl
        JOIN items i ON sl.itemid = i.itemid
        WHERE sl.itemid LIKE ?
        OR sl.lot_no LIKE ?
        OR i.generaldescription LIKE ?
        ORDER BY sl.date_encoded DESC";
$stmt = $conn->prepare($sql);
$searchTerm = "%$search%";
$stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pharmacy Stock Ledger</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 1400px; margin: 20px auto; }
        .card { border-radius: 10px; box-shadow: 0 0 15px rgba(0,0,0,0.1); }
        .card-header { background-color: #0d6efd; color: white; }
        .table th { background-color: #f8f9fa; }
        .btn-group-vertical { gap: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h2 class="mb-0">Pharmacy Stock Ledger</h2>
                <div class="d-flex gap-2">
                    <form method="GET" class="d-flex">
                        <input type="text" class="form-control me-2" name="search" 
                            value="<?php echo htmlspecialchars($search); ?>"
                            placeholder="Search items...">
                        <button class="btn btn-light" type="submit">Search</button>
                    </form>
                    <a href="../pharmacy/pharmacydashboard.php" class="btn btn-light">Home</a>
                </div>
            </div>
            <div class="card-body">
                <!-- Search Controls -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <form method="GET" class="d-flex">
                            <input type="text" class="form-control me-2" name="search" placeholder="Search by Item ID, Lot No, or Description" value="<?php echo htmlspecialchars($search); ?>">
                            <button type="submit" class="btn btn-primary">Search</button>
                        </form>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="add_stock.php" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add Stock
                        </a>
                    </div>
                </div>

                <!-- Simple Stats -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <strong><?php echo count($results); ?></strong><br>
                                    <small>Total Records</small>
                                </div>
                                <div class="col-md-3">
                                    <strong><?php echo count(array_unique(array_column($results, 'itemid'))); ?></strong><br>
                                    <small>Unique Items</small>
                                </div>
                                <div class="col-md-3">
                                    <strong>₱<?php echo number_format(array_sum(array_map(function($r) { return $r['qty_received'] * $r['unit_cost']; }, $results)), 2); ?></strong><br>
                                    <small>Total Value</small>
                                </div>
                                <div class="col-md-3">
                                    <strong><?php echo array_sum(array_column($results, 'qty_received')); ?></strong><br>
                                    <small>Total Quantity</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <form method="POST" class="mb-4 bg-white p-4 rounded-3 shadow-sm border border-primary border-opacity-25">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label text-primary fw-semibold"><i class="fas fa-pills me-1"></i>Item</label>
                            <select class="form-select border-primary border-opacity-25" name="itemid" required>
                                <option value="">Select Medication/Supply</option>
                                <?php
                                $stmt = $conn->query("SELECT itemid, generaldescription FROM items ORDER BY generaldescription");
                                while ($row = $stmt->fetch()) {
                                    echo "<option value='" . htmlspecialchars($row['itemid']) . "'>" .
                                        "ID: " . htmlspecialchars($row['itemid']) . " - " . htmlspecialchars($row['generaldescription']) . "</option>";
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-primary fw-semibold"><i class="fas fa-barcode me-1"></i>Lot No.</label>
                            <input type="text" class="form-control border-primary border-opacity-25" name="lot_no" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-primary fw-semibold"><i class="fas fa-calendar-times me-1"></i>Expiry Date</label>
                            <input type="date" class="form-control border-primary border-opacity-25" name="expiry_date" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-primary fw-semibold"><i class="fas fa-tag me-1"></i>Unit Cost</label>
                            <input type="number" step="0.01" class="form-control border-primary border-opacity-25" name="unit_cost" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-primary fw-semibold"><i class="fas fa-percent me-1"></i>Mark Up</label>
                            <input type="number" step="0.01" class="form-control border-primary border-opacity-25" name="markup" id="markup" required onchange="computeSellingPrice()">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-primary fw-semibold"><i class="fas fa-dollar-sign me-1"></i>Selling Price</label>
                            <input type="number" step="0.01" class="form-control bg-light" name="selling_price" id="selling_price" readonly>
                        </div>
                        <script>
                            function computeSellingPrice() {
                                const unitCost = parseFloat(document.getElementsByName('unit_cost')[0].value) || 0;
                                const markup = parseFloat(document.getElementsByName('markup')[0].value) || 0;
                                const sellingPrice = unitCost * (1 + markup/100);
                                document.getElementsByName('selling_price')[0].value = sellingPrice.toFixed(2);
                            }
                            document.getElementsByName('unit_cost')[0].addEventListener('change', computeSellingPrice);
                        </script>
                        <div class="col-md-2">
                            <label class="form-label text-primary fw-semibold"><i class="fas fa-boxes me-1"></i>Qty Received</label>
                            <input type="number" class="form-control border-primary border-opacity-25" name="qty_received" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-primary fw-semibold"><i class="fas fa-file-alt me-1"></i>CRIS No.</label>
                            <input type="text" class="form-control border-primary border-opacity-25" name="cris_no" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-primary fw-semibold"><i class="fas fa-balance-scale me-1"></i>Beg. Balance</label>
                            <input type="text" class="form-control border-primary border-opacity-25" name="beg_balance" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-primary fw-semibold"><i class="fas fa-calendar-check me-1"></i>Date Received</label>
                            <input type="date" class="form-control border-primary border-opacity-25" name="date_received" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-primary fw-semibold"><i class="fas fa-file-invoice me-1"></i>ICS No.</label>
                            <input type="text" class="form-control border-primary border-opacity-25" name="ics_no" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-primary fw-semibold"><i class="fas fa-truck me-1"></i>Supplier</label>
                            <input type="text" class="form-control border-primary border-opacity-25" name="supplier" required>
                        </div>
                        <div class="col-12 text-end mt-4">
                            <button type="submit" name="submit" class="btn btn-primary px-4">
                                <i class="fas fa-plus-circle me-1"></i>Add to Inventory
                            </button>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Item ID</th>
                                <th>Description</th>
                                <th>Lot No.</th>
                                <th>Expiry Date</th>
                                <th>Unit Cost</th>
                                <th>Selling Price</th>
                                <th>Qty Received</th>
                                <th>CRIS No.</th>
                                <th>Beg. Balance</th>
                                <th>Date Received</th>
                                <th>ICS No.</th>
                                <th>Supplier</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($results)): ?>
                                <tr>
                                    <td colspan="12" class="text-center">No records found</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach($results as $row): ?>
                                    <tr>
                                        <td style="display:none;"><?php echo htmlspecialchars($row['id']); ?></td>
                                        <td><span class="badge bg-primary"><?php echo htmlspecialchars($row['itemid']); ?></span></td>
                                        <td><?php echo htmlspecialchars($row['generaldescription']); ?></td>
                                        <td><?php echo htmlspecialchars($row['lot_no']); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($row['expiry_date'])); ?></td>
                                        <td><?php echo number_format($row['unit_cost'], 2); ?></td>
                                        <td><?php echo number_format($row['selling_price'], 2); ?></td>
                                        <td><?php echo htmlspecialchars($row['qty_received']); ?></td>
                                        <td><?php echo htmlspecialchars($row['cris_no']); ?></td>
                                        <td><?php echo htmlspecialchars($row['beg_balance']); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($row['date_received'])); ?></td>
                                        <td><?php echo htmlspecialchars($row['ics_no']); ?></td>
                                        <td><?php echo htmlspecialchars($row['supplier']); ?></td>
                                        <td>
                                            <div class="btn-group">
                                                <button class="btn btn-warning btn-sm" onclick="editItem(<?php echo htmlspecialchars(json_encode($row)); ?>)">Edit</button>
                                                <button class="btn btn-danger btn-sm" onclick="deleteItem(<?php echo $row['id']; ?>)">Delete</button>
                                                <button class="btn btn-primary btn-sm" onclick="window.open('print_utilization.php?id=<?php echo $row['id']; ?>&item_no=<?php echo $row['item_no']; ?>', '_blank')">
                                                    <i class="fas fa-print me-1"></i>Print
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Medication/Supply Edit Modal -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="fas fa-pills me-2"></i>Edit Pharmaceutical Item</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-4">
                    <form method="POST" id="editForm" class="needs-validation" novalidate>
                        <input type="hidden" name="id" id="edit_id">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-hashtag me-1"></i>Item No.</label>
                                <input type="text" class="form-control border-primary border-opacity-25" name="item_no" id="edit_item_no" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-capsules me-1"></i>Medication/Supply</label>
                                <select class="form-select border-primary border-opacity-25" name="itemid" id="edit_itemid" required>
                                    <?php
                                    $stmt = $conn->query("SELECT itemid, generaldescription FROM items ORDER BY generaldescription");
                                    while ($row = $stmt->fetch()) {
                                        echo "<option value='" . htmlspecialchars($row['itemid']) . "'>" . 
                                            htmlspecialchars($row['generaldescription']) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-barcode me-1"></i>Lot No.</label>
                                <input type="text" class="form-control border-primary border-opacity-25" name="lot_no" id="edit_lot_no" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-calendar-times me-1"></i>Expiry Date</label>
                                <input type="date" class="form-control border-primary border-opacity-25" name="expiry_date" id="edit_expiry_date" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-tag me-1"></i>Unit Cost</label>
                                <div class="input-group">
                                    <span class="input-group-text">₱</span>
                                    <input type="number" step="0.01" class="form-control border-primary border-opacity-25" name="unit_cost" id="edit_unit_cost" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-percent me-1"></i>Mark Up</label>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="form-control border-primary border-opacity-25" name="markup" id="edit_markup" required onchange="computeEditSellingPrice()">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-dollar-sign me-1"></i>Selling Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">₱</span>
                                    <input type="number" step="0.01" class="form-control bg-light" name="selling_price" id="edit_selling_price" readonly>
                                </div>
                            </div>
                            <script>
                                function computeEditSellingPrice() {
                                    const unitCost = parseFloat(document.getElementById('edit_unit_cost').value) || 0;
                                    const markup = parseFloat(document.getElementById('edit_markup').value) || 0;
                                    const sellingPrice = unitCost * (1 + markup/100);
                                    document.getElementById('edit_selling_price').value = sellingPrice.toFixed(2);
                                }
                                document.getElementById('edit_unit_cost').addEventListener('change', computeEditSellingPrice);
                            </script>
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-boxes me-1"></i>Quantity Received</label>
                                <input type="number" class="form-control border-primary border-opacity-25" name="qty_received" id="edit_qty_received" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-file-alt me-1"></i>CRIS No.</label>
                                <input type="text" class="form-control border-primary border-opacity-25" name="cris_no" id="edit_cris_no" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-balance-scale me-1"></i>Beginning Balance</label>
                                <input type="text" class="form-control border-primary border-opacity-25" name="beg_balance" id="edit_beg_balance" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-calendar-check me-1"></i>Date Received</label>
                                <input type="date" class="form-control border-primary border-opacity-25" name="date_received" id="edit_date_received" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-file-invoice me-1"></i>ICS No.</label>
                                <input type="text" class="form-control border-primary border-opacity-25" name="ics_no" id="edit_ics_no" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-primary fw-bold"><i class="fas fa-truck me-1"></i>Supplier</label>
                                <input type="text" class="form-control border-primary border-opacity-25" name="supplier" id="edit_supplier" required>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end gap-2 mt-4 pt-3 border-top">
                            <button type="button" class="btn btn-light px-4" data-bs-dismiss="modal">
                                <i class="fas fa-times me-2"></i>Cancel
                            </button>
                            <button type="submit" name="update" class="btn btn-primary px-4">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete this item?
                </div>
                <div class="modal-footer">
                    <form method="POST">
                        <input type="hidden" name="id" id="delete_id">
                        <button type="submit" name="delete" class="btn btn-danger">Delete</button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editItem(item) {
            document.getElementById('edit_id').value = item.id;
            document.getElementById('edit_item_no').value = item.item_no;
            document.getElementById('edit_itemid').value = item.itemid;
            document.getElementById('edit_lot_no').value = item.lot_no;
            document.getElementById('edit_expiry_date').value = item.expiry_date;
            document.getElementById('edit_unit_cost').value = item.unit_cost;
            document.getElementById('edit_selling_price').value = item.selling_price;
            document.getElementById('edit_qty_received').value = item.qty_received;
            document.getElementById('edit_cris_no').value = item.cris_no;
            document.getElementById('edit_beg_balance').value = item.beg_balance;
            document.getElementById('edit_date_received').value = item.date_received;
            document.getElementById('edit_ics_no').value = item.ics_no;
            document.getElementById('edit_supplier').value = item.supplier;
            
            new bootstrap.Modal(document.getElementById('editModal')).show();
        }

        function deleteItem(id) {
            document.getElementById('delete_id').value = id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
