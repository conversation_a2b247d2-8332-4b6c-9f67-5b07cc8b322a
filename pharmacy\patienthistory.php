<?php
require_once '../database.php';

// Get patient ID from URL parameter
$patientId = isset($_GET['id']) ? $_GET['id'] : null;

if (!$patientId) {
    die("Patient ID is required");
}

// Fetch patient details
$patientQuery = "SELECT * FROM patient WHERE patientid = ?";
$patientStmt = $conn->prepare($patientQuery);
$patientStmt->execute([$patientId]);
$patient = $patientStmt->fetch(PDO::FETCH_ASSOC);

if (!$patient) {
    die("Patient not found");
}

// Fetch transaction history
$historyQuery = "
    SELECT 
        pt.transaction_reference,
        pt.transaction_date,
        pt.OR_Number,
        i.generaldescription,
        i.item_type,
        i.unitmeasure,
        ptd.quantity,
        ptd.unit_price,
        ptd.subtotal
    FROM pharmatransactions pt
    JOIN pharmatransaction_details ptd ON pt.transaction_id = ptd.transaction_id
    JOIN items i ON ptd.item_id = i.itemid
    WHERE pt.patientid = ?
    ORDER BY pt.transaction_date DESC";
$historyStmt = $conn->prepare($historyQuery);
$historyStmt->execute([$patientId]);
$transactions = $historyStmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Purchase History</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <!-- Patient Info Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-user-circle me-2"></i>Patient Information</h4>
            </div>
            <div class="card-body">
                <h5><?php echo htmlspecialchars($patient['patientname']); ?></h5>
                <p class="mb-1">Patient Type: <?php echo htmlspecialchars($patient['patient_type']); ?></p>
                <p class="mb-1">ID Number: <?php echo htmlspecialchars($patient['id_number']); ?></p>
            </div>
        </div>

        <!-- Transaction History -->
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-history me-2"></i>Purchase History</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Reference</th>
                                <th>OR Number</th>
                                <th>Item Description</th>
                                <th>Type</th>
                                <th>Unit</th>
                                <th>Quantity</th>
                                <th>Unit Price</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($transactions as $transaction): ?>
                            <tr>
                                <td><?php echo date('M d, Y', strtotime($transaction['transaction_date'])); ?></td>
                                <td><?php echo htmlspecialchars($transaction['transaction_reference']); ?></td>
                                <td><?php echo htmlspecialchars($transaction['OR_Number']); ?></td>
                                <td><?php echo htmlspecialchars($transaction['generaldescription']); ?></td>
                                <td><?php echo htmlspecialchars($transaction['item_type']); ?></td>
                                <td><?php echo htmlspecialchars($transaction['unitmeasure']); ?></td>
                                <td><?php echo htmlspecialchars($transaction['quantity']); ?></td>
                                <td>₱<?php echo number_format($transaction['unit_price'], 2); ?></td>
                                <td>₱<?php echo number_format($transaction['subtotal'], 2); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
