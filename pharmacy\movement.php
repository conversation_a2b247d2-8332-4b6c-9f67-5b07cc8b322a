<?php
require_once '../database.php';

// Get selected month or default to current month
$selectedMonth = isset($_GET['month']) ? $_GET['month'] . '-01' : date('Y-m-01');
$selectedMonthEnd = date('Y-m-t', strtotime($selectedMonth));

// Query to get item movement categories
$sql = "WITH ItemMovement AS (
    SELECT 
        i.itemid,
        i.generaldescription,
        i.unitmeasure,
        pc.categorydesc,
        SUM(ptd.quantity) as total_quantity,
        COUNT(DISTINCT pt.transaction_id) as transaction_count,
        CASE 
            WHEN SUM(ptd.quantity) > 100 THEN 'Fast Moving'
            WHEN SUM(ptd.quantity) BETWEEN 50 AND 100 THEN 'Moderate Moving'
            ELSE 'Slow Moving'
        END as movement_category
    FROM items i
    LEFT JOIN pharmacy_stock_ledger psl ON i.itemid=psl.itemid
    LEFT JOIN pharmatransaction_details ptd ON psl.item_no = ptd.item_no
    LEFT JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
    LEFT JOIN pharmacategory pc ON i.category = pc.categoryid
    WHERE pt.transaction_date BETWEEN ? AND ?
    GROUP BY i.itemid, i.generaldescription, i.unitmeasure, pc.categorydesc
)
SELECT * FROM ItemMovement ORDER BY total_quantity DESC";

$stmt = $conn->prepare($sql);
$stmt->execute([$selectedMonth, $selectedMonthEnd]);
$itemMovements = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Group items by movement category
$movementCategories = [
    'Fast Moving' => [],
    'Moderate Moving' => [],
    'Slow Moving' => []
];

foreach ($itemMovements as $item) {
    $movementCategories[$item['movement_category']][] = $item;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Item Movement Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Monthly Item Movement Report
                    </h4>
                    <div class="d-flex align-items-center">
                        <form class="me-3" method="GET">
                            <div class="input-group">
                                <input type="month" class="form-control" name="month" 
                                       value="<?php echo date('Y-m', strtotime($selectedMonth)); ?>"
                                       onchange="this.form.submit()">
                            </div>
                        </form>
                        <a href="../pharmacy/pharmacydashboard.php" class="btn btn-light btn-lg rounded-3">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs mb-4" id="movementTabs" role="tablist">
                    <?php $first = true; foreach ($movementCategories as $category => $items): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php echo $first ? 'active' : ''; ?>" 
                                id="<?php echo str_replace(' ', '-', strtolower($category)); ?>-tab" 
                                data-bs-toggle="tab" 
                                data-bs-target="#<?php echo str_replace(' ', '-', strtolower($category)); ?>" 
                                type="button" role="tab">
                            <i class="fas <?php echo $category === 'Fast Moving' ? 'fa-bolt' : 
                                                ($category === 'Moderate Moving' ? 'fa-arrow-right' : 'fa-turtle'); ?> me-2"></i>
                            <?php echo $category; ?>
                        </button>
                    </li>
                    <?php $first = false; endforeach; ?>
                </ul>

                <div class="tab-content" id="movementTabContent">
                    <?php $first = true; foreach ($movementCategories as $category => $items): ?>
                    <div class="tab-pane fade <?php echo $first ? 'show active' : ''; ?>" 
                         id="<?php echo str_replace(' ', '-', strtolower($category)); ?>" 
                         role="tabpanel">
                        <div class="alert alert-info">
                            <?php
                            switch($category) {
                                case 'Fast Moving':
                                    echo '<strong>High Demand Items:</strong> These items have moved more than 100 units in the selected period. They require frequent restocking and close inventory monitoring.';
                                    break;
                                case 'Moderate Moving':
                                    echo '<strong>Medium Demand Items:</strong> These items have moved between 50 to 100 units. They maintain a steady demand and require regular inventory checks.';
                                    break;
                                case 'Slow Moving':
                                    echo '<strong>Low Demand Items:</strong> These items have moved less than 50 units. Consider reviewing stock levels and procurement strategy for these items.';
                                    break;
                            }
                            ?>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover border">
                                <thead class="table-light">
                                    <tr>
                                        <th>Item Description</th>
                                        <th>Category</th>
                                        <th>Unit</th>
                                        <th>Total Quantity</th>
                                        <th>Transaction Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($items as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['generaldescription']); ?></td>
                                        <td><span class="badge bg-info"><?php echo htmlspecialchars($item['categorydesc']); ?></span></td>
                                        <td><?php echo htmlspecialchars($item['unitmeasure']); ?></td>
                                        <td class="fw-bold"><?php echo number_format($item['total_quantity']); ?></td>
                                        <td><?php echo number_format($item['transaction_count']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <?php $first = false; endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
