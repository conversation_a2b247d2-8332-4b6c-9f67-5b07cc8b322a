<?php
require_once '../database.php';

// Get selected month or default to current month
$selectedMonth = isset($_GET['month']) ? $_GET['month'] . '-01' : date('Y-m-01');
$selectedMonthEnd = date('Y-m-t', strtotime($selectedMonth));

// Enhanced analytics query with detailed frequency metrics
$analyticsQuery = "
WITH DetailedMovement AS (
    SELECT 
        i.itemid,
        i.generaldescription,
        i.unitmeasure,
        pc.categorydesc,
        SUM(ptd.quantity) as total_quantity,
        COUNT(DISTINCT pt.transaction_id) as transaction_count,
        COUNT(DISTINCT DATE(pt.transaction_date)) as active_days,
        COUNT(DISTINCT WEEK(pt.transaction_date)) as active_weeks,
        AVG(ptd.quantity) as avg_quantity_per_transaction,
        DATEDIFF(?, ?) + 1 as total_days_in_period,
        CEIL((DATEDIFF(?, ?) + 1) / 7) as total_weeks_in_period,
        
        -- Frequency calculations
        COUNT(DISTINCT pt.transaction_id) / (DATEDIFF(?, ?) + 1) as transactions_per_day,
        COUNT(DISTINCT DATE(pt.transaction_date)) / (DATEDIFF(?, ?) + 1) as usage_consistency,
        SUM(ptd.quantity) / COUNT(DISTINCT DATE(pt.transaction_date)) as avg_daily_usage,
        COUNT(DISTINCT pt.transaction_id) / CEIL((DATEDIFF(?, ?) + 1) / 7) as transactions_per_week,
        COUNT(DISTINCT DATE(pt.transaction_date)) / CEIL((DATEDIFF(?, ?) + 1) / 7) as active_days_per_week,
        
        -- Peak usage analysis
        MAX(daily_usage.daily_total) as peak_daily_usage,
        MIN(daily_usage.daily_total) as min_daily_usage,
        STDDEV(daily_usage.daily_total) as usage_variance
        
    FROM items i
    LEFT JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid
    LEFT JOIN pharmatransaction_details ptd ON psl.item_no = ptd.item_no
    LEFT JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
    LEFT JOIN pharmacategory pc ON i.category = pc.categoryid
    LEFT JOIN (
        SELECT 
            psl2.itemid,
            DATE(pt2.transaction_date) as usage_date,
            SUM(ptd2.quantity) as daily_total
        FROM pharmacy_stock_ledger psl2
        JOIN pharmatransaction_details ptd2 ON psl2.item_no = ptd2.item_no
        JOIN pharmatransactions pt2 ON ptd2.transaction_id = pt2.transaction_id
        WHERE pt2.transaction_date BETWEEN ? AND ?
        GROUP BY psl2.itemid, DATE(pt2.transaction_date)
    ) daily_usage ON i.itemid = daily_usage.itemid
    
    WHERE pt.transaction_date BETWEEN ? AND ?
    GROUP BY i.itemid, i.generaldescription, i.unitmeasure, pc.categorydesc
    HAVING COUNT(DISTINCT pt.transaction_id) > 0
),
EnhancedClassification AS (
    SELECT *,
        -- Enhanced classification with multiple criteria
        CASE 
            WHEN transactions_per_week >= 5 AND usage_consistency >= 0.5 THEN 'Critical Fast Moving'
            WHEN transactions_per_week >= 3 OR total_quantity >= 100 OR usage_consistency >= 0.4 THEN 'Fast Moving'
            WHEN (transactions_per_week >= 1.5 AND transactions_per_week < 3) OR 
                 (total_quantity >= 25 AND total_quantity < 100) OR 
                 (usage_consistency >= 0.15 AND usage_consistency < 0.4) THEN 'Moderate Moving'
            WHEN transactions_per_week >= 0.5 OR total_quantity >= 10 OR usage_consistency >= 0.05 THEN 'Slow Moving'
            ELSE 'Very Slow Moving'
        END as movement_category,
        
        -- Risk assessment
        CASE 
            WHEN usage_variance > avg_daily_usage * 2 THEN 'High Variability'
            WHEN usage_variance > avg_daily_usage THEN 'Medium Variability'
            ELSE 'Low Variability'
        END as usage_pattern,
        
        -- Reorder priority
        CASE 
            WHEN transactions_per_week >= 3 AND usage_consistency >= 0.3 THEN 'High Priority'
            WHEN transactions_per_week >= 1 AND usage_consistency >= 0.15 THEN 'Medium Priority'
            ELSE 'Low Priority'
        END as reorder_priority
        
    FROM DetailedMovement
)
SELECT * FROM EnhancedClassification 
ORDER BY transactions_per_week DESC, total_quantity DESC, usage_consistency DESC
";

$stmt = $conn->prepare($analyticsQuery);
$stmt->execute([
    $selectedMonthEnd, $selectedMonth,    // DATEDIFF calculations
    $selectedMonthEnd, $selectedMonth,    // total_weeks_in_period
    $selectedMonthEnd, $selectedMonth,    // transactions_per_day
    $selectedMonthEnd, $selectedMonth,    // usage_consistency
    $selectedMonthEnd, $selectedMonth,    // transactions_per_week
    $selectedMonthEnd, $selectedMonth,    // active_days_per_week
    $selectedMonth, $selectedMonthEnd,    // daily_usage subquery
    $selectedMonth, $selectedMonthEnd     // main WHERE clause
]);

$analyticsData = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Group by enhanced categories
$enhancedCategories = [
    'Critical Fast Moving' => [],
    'Fast Moving' => [],
    'Moderate Moving' => [],
    'Slow Moving' => [],
    'Very Slow Moving' => []
];

foreach ($analyticsData as $item) {
    $enhancedCategories[$item['movement_category']][] = $item;
}

// Calculate summary statistics
$totalItems = count($analyticsData);
$avgTransactionsPerWeek = array_sum(array_column($analyticsData, 'transactions_per_week')) / $totalItems;
$avgConsistency = array_sum(array_column($analyticsData, 'usage_consistency')) / $totalItems;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Movement Analytics</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Advanced Movement Analytics
                    </h4>
                    <div class="d-flex align-items-center">
                        <form class="me-3" method="GET">
                            <div class="input-group">
                                <input type="month" class="form-control" name="month" 
                                       value="<?php echo date('Y-m', strtotime($selectedMonth)); ?>"
                                       onchange="this.form.submit()">
                            </div>
                        </form>
                        <a href="movement.php" class="btn btn-light me-2">
                            <i class="fas fa-arrow-left me-2"></i>Basic View
                        </a>
                        <a href="../pharmacy/pharmacydashboard.php" class="btn btn-light">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Summary Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-pills fa-2x mb-2"></i>
                                <h4><?php echo $totalItems; ?></h4>
                                <small>Total Active Items</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <h4><?php echo number_format($avgTransactionsPerWeek, 1); ?></h4>
                                <small>Avg Transactions/Week</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-percentage fa-2x mb-2"></i>
                                <h4><?php echo number_format($avgConsistency * 100, 1); ?>%</h4>
                                <small>Avg Usage Consistency</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <h4><?php echo count($enhancedCategories['Critical Fast Moving']); ?></h4>
                                <small>Critical Items</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Category Distribution -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-pie-chart me-2"></i>Movement Distribution</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="movementChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list me-2"></i>Category Breakdown</h5>
                            </div>
                            <div class="card-body">
                                <?php foreach ($enhancedCategories as $category => $items): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge <?php 
                                        echo $category === 'Critical Fast Moving' ? 'bg-danger' : 
                                            ($category === 'Fast Moving' ? 'bg-success' : 
                                            ($category === 'Moderate Moving' ? 'bg-warning' : 
                                            ($category === 'Slow Moving' ? 'bg-info' : 'bg-secondary'))); 
                                    ?>"><?php echo $category; ?></span>
                                    <strong><?php echo count($items); ?> items</strong>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Analytics Tables -->
                <ul class="nav nav-tabs mb-4" id="analyticsTab" role="tablist">
                    <?php $first = true; foreach ($enhancedCategories as $category => $items): ?>
                    <?php if (!empty($items)): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php echo $first ? 'active' : ''; ?>"
                                id="<?php echo str_replace(' ', '-', strtolower($category)); ?>-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#<?php echo str_replace(' ', '-', strtolower($category)); ?>"
                                type="button" role="tab">
                            <?php echo $category; ?> (<?php echo count($items); ?>)
                        </button>
                    </li>
                    <?php $first = false; endif; ?>
                    <?php endforeach; ?>
                </ul>

                <div class="tab-content" id="analyticsTabContent">
                    <?php $first = true; foreach ($enhancedCategories as $category => $items): ?>
                    <?php if (!empty($items)): ?>
                    <div class="tab-pane fade <?php echo $first ? 'show active' : ''; ?>"
                         id="<?php echo str_replace(' ', '-', strtolower($category)); ?>"
                         role="tabpanel">

                        <div class="table-responsive">
                            <table class="table table-hover table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Item Description</th>
                                        <th>Category</th>
                                        <th>Total Qty</th>
                                        <th>Freq/Week</th>
                                        <th>Consistency</th>
                                        <th>Avg Daily</th>
                                        <th>Peak Usage</th>
                                        <th>Variability</th>
                                        <th>Priority</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($items as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['generaldescription']); ?></td>
                                        <td><span class="badge bg-info"><?php echo htmlspecialchars($item['categorydesc']); ?></span></td>
                                        <td class="fw-bold"><?php echo number_format($item['total_quantity']); ?></td>
                                        <td>
                                            <span class="badge <?php echo $item['transactions_per_week'] >= 3 ? 'bg-success' :
                                                                        ($item['transactions_per_week'] >= 1 ? 'bg-warning' : 'bg-danger'); ?>">
                                                <?php echo number_format($item['transactions_per_week'], 1); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $item['usage_consistency'] >= 0.4 ? 'bg-success' :
                                                                        ($item['usage_consistency'] >= 0.15 ? 'bg-warning' : 'bg-danger'); ?>">
                                                <?php echo number_format($item['usage_consistency'] * 100, 1); ?>%
                                            </span>
                                        </td>
                                        <td><?php echo number_format($item['avg_daily_usage'], 1); ?></td>
                                        <td class="text-danger fw-bold"><?php echo number_format($item['peak_daily_usage'] ?? 0); ?></td>
                                        <td>
                                            <span class="badge <?php echo $item['usage_pattern'] === 'High Variability' ? 'bg-danger' :
                                                                        ($item['usage_pattern'] === 'Medium Variability' ? 'bg-warning' : 'bg-success'); ?>">
                                                <?php echo $item['usage_pattern']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $item['reorder_priority'] === 'High Priority' ? 'bg-danger' :
                                                                        ($item['reorder_priority'] === 'Medium Priority' ? 'bg-warning' : 'bg-secondary'); ?>">
                                                <?php echo $item['reorder_priority']; ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <?php $first = false; endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Movement Distribution Chart
        const ctx = document.getElementById('movementChart').getContext('2d');
        const movementChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: [
                    <?php foreach ($enhancedCategories as $category => $items): ?>
                    '<?php echo $category; ?> (<?php echo count($items); ?>)',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    data: [
                        <?php foreach ($enhancedCategories as $category => $items): ?>
                        <?php echo count($items); ?>,
                        <?php endforeach; ?>
                    ],
                    backgroundColor: [
                        '#dc3545', // Critical Fast Moving - Red
                        '#28a745', // Fast Moving - Green
                        '#ffc107', // Moderate Moving - Yellow
                        '#17a2b8', // Slow Moving - Blue
                        '#6c757d'  // Very Slow Moving - Gray
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
