<?php
require_once '../database.php';

// Get all items for the dropdown
$items_sql = "SELECT itemid, generaldescription FROM items ORDER BY generaldescription";
$items_stmt = $conn->prepare($items_sql);
$items_stmt->execute();
$items = $items_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get selected filters
$selected_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
$selected_item = isset($_GET['item_id']) ? $_GET['item_id'] : '';

// Get utilization report data with filters (updated to use itemid)
$sql = "SELECT
    DATE(pt.transaction_date) AS transaction_date,
    i.generaldescription AS item_name,
    p.patientname,
    SUM(pd.quantity) AS quantity_utilized
FROM pharmatransactions pt
JOIN pharmatransaction_details pd ON pt.transaction_id = pd.transaction_id
JOIN patient p ON pt.patientid = p.patientid
JOIN items i ON pd.itemid = i.itemid
WHERE DATE(pt.transaction_date) = :date " .
($selected_item ? "AND i.itemid = :item_id " : "") .
"GROUP BY DATE(pt.transaction_date), i.generaldescription, p.patientname
ORDER BY transaction_date, item_name, p.patientname";

$stmt = $conn->prepare($sql);
$stmt->bindParam(':date', $selected_date);
if ($selected_item) {
    $stmt->bindParam(':item_id', $selected_item);
}
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Utilization Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background-color: #f0f5ff;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }
        .container { max-width: 1200px; margin: 20px auto; }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #0d6efd, #0099ff);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e0e0e0;
            padding: 10px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
        }
        .btn-primary {
            background: linear-gradient(135deg, #0d6efd, #0099ff);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
        }
        .table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow-lg">
            <div class="card-header py-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">
                        <i class="fas fa-pills me-2"></i>Pharmacy Daily Utilization Report
                    </h3>
                    <a href="../pharmacy/pharmacydashboard.php" class="btn btn-light btn-lg rounded-3">
                        <i class="fas fa-home me-2"></i>Homepage
                    </a>
                </div>
            </div>

            <div class="card-body">
                <!-- Filter Form -->
                <form method="GET" class="row g-3 mb-4">
                    <div class="col-md-4">
                        <label class="form-label">
                            <i class="fas fa-calendar me-2"></i>Select Date
                        </label>
                        <input type="date" class="form-control" name="date" value="<?php echo $selected_date; ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">
                            <i class="fas fa-capsules me-2"></i>Select Medicine/Item
                        </label>
                        <select class="form-select" name="item_id">
                            <option value="">All Items</option>
                            <?php foreach($items as $item): ?>
                                <option value="<?php echo $item['itemid']; ?>" 
                                    <?php echo ($selected_item == $item['itemid']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($item['generaldescription']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th><i class="fas fa-calendar me-2"></i>Date</th>
                                <th><i class="fas fa-pills me-2"></i>Item Name</th>
                                <th><i class="fas fa-user me-2"></i>Patient Name</th>
                                <th><i class="fas fa-box me-2"></i>Quantity Utilized</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($results)): ?>
                            <tr>
                                <td colspan="4" class="text-center py-4">
                                    <i class="fas fa-info-circle me-2"></i>No records found for the selected filters.
                                </td>
                            </tr>
                            <?php else: ?>
                                <?php foreach($results as $row): ?>
                                <tr>
                                    <td><?php echo date('M d, Y', strtotime($row['transaction_date'])); ?></td>
                                    <td><?php echo htmlspecialchars($row['item_name']); ?></td>
                                    <td><?php echo htmlspecialchars($row['patientname']); ?></td>
                                    <td><?php echo number_format($row['quantity_utilized']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
