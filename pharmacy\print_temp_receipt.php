<?php
require_once '../database.php';
require_once '../fpdf/fpdf.php';

// Get transaction ID from URL
$transactionId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get transaction details
$sql = "SELECT t.*, p.patientname, p.patient_type 
        FROM pharmatransactions t 
        JOIN patient p ON t.patientid = p.patientid 
        WHERE t.transaction_id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$transactionId]);
$transaction = $stmt->fetch();

// Get transaction items with category
$sql = "SELECT d.*, i.generaldescription, i.unitmeasure, i.item_type 
        FROM pharmatransaction_details d
        JOIN pharmacy_stock_ledger psl ON d.item_no=psl.item_no 
        JOIN items i ON psl.itemid = i.itemid 
        WHERE d.transaction_id = ?
        ORDER BY i.item_type, i.generaldescription";
$stmt = $conn->prepare($sql);
$stmt->execute([$transactionId]);
$transactionItems = $stmt->fetchAll();

// Group items by category
$groupedItems = [];
foreach ($transactionItems as $item) {
    if ($item['item_type'] == 'Medical Supply') {
        if (!isset($groupedItems['Medical Supply'])) {
            $groupedItems['Medical Supply'] = [];
        }
        $groupedItems['Medical Supply'][] = $item;
    } elseif ($item['item_type'] == 'Medicine') {
        if (!isset($groupedItems['Medicine'])) {
            $groupedItems['Medicine'] = [];
        }
        $groupedItems['Medicine'][] = $item;
    } else {
        if (!isset($groupedItems['Other'])) {
            $groupedItems['Other'] = [];
        }
        $groupedItems['Other'][] = $item;
    }
}

// Create PDF (80mm thermal paper width - scaled to 93%)
$pageWidth = 80 * 0.93;
$pdf = new FPDF('P', 'mm', array($pageWidth, 200 * 0.93)); // Width & Height scaled to 93%
$pdf->AddPage();
$pdf->SetMargins(5 * 0.93, 2 * 0.93, 5 * 0.93); // Margins scaled to 93%

// Slow down printing by increasing line height and font size
$pdf->SetAutoPageBreak(true, 10 * 0.93);

// Header section
$pdf->SetFont('Arial', 'B', 10 * 0.93); // Font size scaled to 93%
$pdf->Cell(0, 5 * 0.93, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
$pdf->SetFont('Arial', '', 9 * 0.93);
$pdf->Cell(0, 4 * 0.93, 'Biri Northern Samar', 0, 1, 'C');
$pdf->Cell(0, 4 * 0.93, 'Province of Northern Samar', 0, 1, 'C');

// Receipt title
$pdf->Ln(2 * 0.93);
$pdf->SetFont('Arial', 'B', 10 * 0.93);
$pdf->Cell(0, 5 * 0.93, 'TEMPORARY RECEIPT', 0, 1, 'C');

// Transaction details
$pdf->SetFont('Arial', '', 9 * 0.93);
$pdf->Ln(2 * 0.93);
$pdf->Cell(0, 4 * 0.93, 'Trans #: ' . $transaction['transaction_reference'], 0, 1);
$pdf->Cell(0, 4 * 0.93, 'Date: ' . date('M d, Y', strtotime($transaction['transaction_date'])), 0, 1);
$pdf->Cell(0, 4 * 0.93, 'Patient: ' . $transaction['patientname'], 0, 1);
$pdf->Cell(0, 4 * 0.93, 'Type: ' . $transaction['patient_type'], 0, 1);
$pdf->Cell(0, 4 * 0.93, 'Ward: ' . $transaction['ward'], 0, 1);

// Separator line
$pdf->Ln(2 * 0.93);
$pdf->Cell(0, 0, '', 'T');
$pdf->Ln(2 * 0.93);

// Items header
$pdf->SetFont('Arial', 'B', 9 * 0.93);
$pdf->Cell(35 * 0.93, 4 * 0.93, 'Item', 0);
$pdf->Cell(10 * 0.93, 4 * 0.93, 'Qty', 0);
$pdf->Cell(15 * 0.93, 4 * 0.93, 'Price', 0, 0, '');
$pdf->Cell(10 * 0.93, 4 * 0.93, 'Total', 0, 0, 'R');
$pdf->Ln(5 * 0.93);

// Items content grouped by category
$pdf->SetFont('Arial', '', 8 * 0.93);
$total = 0;
$totalDiscount = 0;

foreach ($groupedItems as $category => $items) {
    // Print category header
    $pdf->SetFont('Arial', 'B', 8 * 0.93);
    if ($category == 'Medical Supply') {
        $pdf->Cell(0, 4 * 0.93, 'MEDICAL SUPPLIES', 0, 1, 'L');
    } elseif ($category == 'Medicine') {
        $pdf->Cell(0, 4 * 0.93, 'MEDICINES', 0, 1, 'L');
    } else {
        $pdf->Cell(0, 4 * 0.93, $category, 0, 1, 'L');
    }
    $pdf->SetFont('Arial', '', 8 * 0.93);
    
    foreach ($items as $item) {
        // Item name with fixed width
        $pdf->MultiCell(35 * 0.93, 4 * 0.93, $item['generaldescription'], 0, 'L');
        $pdf->SetX($pdf->GetX() + (35 * 0.93));
        $pdf->Cell(10 * 0.93, 4 * 0.93, $item['quantity'], 0);
        $pdf->Cell(15 * 0.93, 4 * 0.93, number_format($item['unit_price'], 2), 0, 0, 'L');
        $pdf->Cell(10 * 0.93, 4 * 0.93, number_format($item['subtotal'], 2), 0, 0, 'R');
        $pdf->Ln();
        
        $total += $item['subtotal'];
        $totalDiscount += $item['discount_applied'];
    }
    
    // Add small spacing between categories
    $pdf->Ln(2 * 0.93);
}

// Separator line
$pdf->Ln(2 * 0.93);
$pdf->Cell(0, 0, '', 'T');
$pdf->Ln(2 * 0.93);

// Totals
$pdf->SetFont('Arial', 'B', 9 * 0.93);
$pdf->Cell(45 * 0.93, 4 * 0.93, 'Total Discount :', 0);
$pdf->Cell(25 * 0.93, 4 * 0.93, 'PHP ' . number_format($totalDiscount, 2), 0, 0, 'R');
$pdf->Ln();
$pdf->Cell(45 * 0.93, 4 * 0.93, 'Total Amount To Pay:', 0);
$pdf->Cell(25 * 0.93, 4 * 0.93, 'PHP ' . number_format($total, 2), 0, 0, 'R');

// Footer
$pdf->Ln(5 * 0.93);
$pdf->SetFont('Arial', 'I', 8 * 0.93);
$pdf->MultiCell(0, 4 * 0.93, 'This is a temporary receipt. Official receipt will be issued upon payment.', 0, 'C');

// Signature Section
$pdf->Ln(6);

// Issued by
$pdf->SetFont('Arial', 'B', 8);
$pdf->Cell(0, 5, 'Issued by:', 0, 1, 'C');
$pdf->Ln(4);
// Get pharmacist name
$pharmacist = isset($_GET['pharmacist']) ? $_GET['pharmacist'] : 'RONNIE B. CELIS, RPh';

// Signature names
$pdf->SetFont('Arial', 'B', 7);
$pdf->Cell(0, 5, $pharmacist, 0, 1, 'C');

// First signature line
$pdf->SetDrawColor(28, 58, 95);
$pdf->SetLineWidth(0.3);
$centerX = $pageWidth / 2;
$lineWidth = 50;
$pdf->Line($centerX - $lineWidth/2, $pdf->GetY() + 1, $centerX + $lineWidth/2, $pdf->GetY() + 1);

// First position title
$pdf->Ln(1);
$pdf->SetFont('Arial', 'I', 7);
$pdf->Cell(0, 5, 'Pharmacist', 0, 1, 'C');

// Add space between signatures
$pdf->Ln(5);

// Received by
$pdf->SetFont('Arial', 'B', 8);
$pdf->Cell(0, 5, 'Received by:', 0, 1, 'C');

// Empty space for recipient signature
$pdf->Cell(0, 5, '', 0, 1, 'C');

// Second signature line
$pdf->Line($centerX - $lineWidth/2, $pdf->GetY() + 1, $centerX + $lineWidth/2, $pdf->GetY() + 1);

// Second position title
$pdf->Ln(1);
$pdf->SetFont('Arial', 'I', 7);
$pdf->Cell(0, 5, 'Patient/Client', 0, 1, 'C');

// Add slight delay before output to ensure proper printing
usleep(500000); // 0.5 second delay
$pdf->Output();
