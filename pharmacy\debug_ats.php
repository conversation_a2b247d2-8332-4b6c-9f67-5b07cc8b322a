<?php
require_once '../database.php';

echo "<h2>Debug ATS 3000 iu /2.0mL Item</h2>";

// 1. Check if item exists in items table
echo "<h3>1. Items Table Check</h3>";
$sql = "SELECT * FROM items WHERE generaldescription LIKE '%ATS%3000%'";
$stmt = $conn->query($sql);
$items = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($items)) {
    echo "<div style='color: red;'>❌ No ATS 3000 items found in items table</div>";
} else {
    echo "<div style='color: green;'>✅ Found " . count($items) . " ATS 3000 items:</div>";
    foreach ($items as $item) {
        echo "<div>- ID: {$item['itemid']}, Name: {$item['generaldescription']}</div>";
    }
}

// 2. Check stock ledger entries
echo "<h3>2. Stock Ledger Check</h3>";
if (!empty($items)) {
    foreach ($items as $item) {
        $sql = "SELECT * FROM pharmacy_stock_ledger WHERE itemid = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$item['itemid']]);
        $stockEntries = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div><strong>Item ID {$item['itemid']}:</strong></div>";
        if (empty($stockEntries)) {
            echo "<div style='color: red;'>❌ No stock ledger entries</div>";
        } else {
            echo "<div style='color: green;'>✅ Found " . count($stockEntries) . " stock entries:</div>";
            foreach ($stockEntries as $stock) {
                echo "<div>- Item No: {$stock['item_no']}, Qty: {$stock['qty_received']}, Date: {$stock['date_received']}</div>";
            }
        }
    }
}

// 3. Check transaction details
echo "<h3>3. Transaction Details Check</h3>";
if (!empty($items)) {
    foreach ($items as $item) {
        $sql = "SELECT ptd.*, pt.transaction_date 
                FROM pharmacy_stock_ledger psl
                JOIN pharmatransaction_details ptd ON psl.item_no = ptd.item_no
                JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
                WHERE psl.itemid = ?
                ORDER BY pt.transaction_date DESC";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$item['itemid']]);
        $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div><strong>Item ID {$item['itemid']} Transactions:</strong></div>";
        if (empty($transactions)) {
            echo "<div style='color: red;'>❌ No transaction entries</div>";
        } else {
            echo "<div style='color: green;'>✅ Found " . count($transactions) . " transactions:</div>";
            foreach ($transactions as $trans) {
                echo "<div>- Date: {$trans['transaction_date']}, Qty: {$trans['quantity']}, Item No: {$trans['item_no']}</div>";
            }
        }
    }
}

// 4. Check current month transactions specifically
echo "<h3>4. Current Month Transactions</h3>";
$currentMonth = date('Y-m-01');
$currentMonthEnd = date('Y-m-t');

if (!empty($items)) {
    foreach ($items as $item) {
        $sql = "SELECT ptd.*, pt.transaction_date 
                FROM pharmacy_stock_ledger psl
                JOIN pharmatransaction_details ptd ON psl.item_no = ptd.item_no
                JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
                WHERE psl.itemid = ? AND pt.transaction_date BETWEEN ? AND ?
                ORDER BY pt.transaction_date DESC";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$item['itemid'], $currentMonth, $currentMonthEnd]);
        $monthTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div><strong>Item ID {$item['itemid']} - Current Month ({$currentMonth} to {$currentMonthEnd}):</strong></div>";
        if (empty($monthTransactions)) {
            echo "<div style='color: orange;'>⚠️ No transactions this month</div>";
        } else {
            echo "<div style='color: green;'>✅ Found " . count($monthTransactions) . " transactions this month:</div>";
            foreach ($monthTransactions as $trans) {
                echo "<div>- Date: {$trans['transaction_date']}, Qty: {$trans['quantity']}</div>";
            }
        }
    }
}

// 5. Test the MUR query logic
echo "<h3>5. MUR Query Test</h3>";
if (!empty($items)) {
    $testSql = "
    WITH AllActiveItems AS (
        SELECT DISTINCT i.itemid
        FROM items i
        WHERE EXISTS (
            SELECT 1 FROM pharmacy_stock_ledger psl WHERE psl.itemid = i.itemid
        ) OR EXISTS (
            SELECT 1 FROM pharmacy_stock_ledger psl2 
            JOIN pharmatransaction_details ptd ON psl2.item_no = ptd.item_no
            JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
            WHERE psl2.itemid = i.itemid AND pt.transaction_date BETWEEN ? AND ?
        )
    )
    SELECT i.itemid, i.generaldescription, 'Found in AllActiveItems' as status
    FROM items i
    INNER JOIN AllActiveItems ai ON i.itemid = ai.itemid
    WHERE i.generaldescription LIKE '%ATS%3000%'
    ";
    
    $stmt = $conn->prepare($testSql);
    $stmt->execute([$currentMonth, $currentMonthEnd]);
    $testResults = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($testResults)) {
        echo "<div style='color: red;'>❌ ATS item not found in AllActiveItems CTE</div>";
    } else {
        echo "<div style='color: green;'>✅ ATS item found in AllActiveItems CTE:</div>";
        foreach ($testResults as $result) {
            echo "<div>- ID: {$result['itemid']}, Name: {$result['generaldescription']}</div>";
        }
    }
}

// 6. Check for any data inconsistencies
echo "<h3>6. Data Consistency Check</h3>";
$sql = "SELECT 
            COUNT(DISTINCT i.itemid) as total_items,
            COUNT(DISTINCT psl.itemid) as items_with_stock,
            COUNT(DISTINCT psl2.itemid) as items_with_transactions
        FROM items i
        LEFT JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid
        LEFT JOIN pharmacy_stock_ledger psl2 ON i.itemid = psl2.itemid
        LEFT JOIN pharmatransaction_details ptd ON psl2.item_no = ptd.item_no";

$stmt = $conn->query($sql);
$consistency = $stmt->fetch(PDO::FETCH_ASSOC);

echo "<div>Total Items: {$consistency['total_items']}</div>";
echo "<div>Items with Stock: {$consistency['items_with_stock']}</div>";
echo "<div>Items with Transactions: {$consistency['items_with_transactions']}</div>";

echo "<br><a href='mur.php'>← Back to MUR Report</a>";
?>
