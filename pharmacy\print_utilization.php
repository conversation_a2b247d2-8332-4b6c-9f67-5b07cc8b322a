<?php
require_once '../database.php';
require_once '../fpdf/fpdf.php';

// Get parameters from URL
$item_no = isset($_GET['item_no']) ? $_GET['item_no'] : '';

// Get utilization details and stock ledger information
$sql = "WITH RunningBalance AS (
    SELECT 
        DATE(pt.transaction_date) as transaction_date,
        d.quantity as quantity_issued,
        i.generaldescription AS item_description,
        i.unitmeasure,
        pc.categorydesc,
        psl.lot_no,
        psl.expiry_date,
        psl.unit_cost,
        psl.selling_price,
        psl.qty_received,
        psl.cris_no,
        psl.beg_balance,
        psl.date_received,
        psl.ics_no,
        psl.supplier,
        p.patientname,
        p.patient_type,
        doc.doctorname,
        (COALESCE(psl.qty_received, 0) + COALESCE(psl.beg_balance, 0)) - 
        SUM(COALESCE(d.quantity, 0)) OVER (
            PARTITION BY psl.item_no 
            ORDER BY pt.transaction_date
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) as running_balance
    FROM pharmacy_stock_ledger psl
    LEFT JOIN items i ON psl.itemid = i.itemid
    LEFT JOIN pharmacategory pc ON i.category = pc.categoryid
    LEFT JOIN pharmatransaction_details d ON d.item_no = psl.item_no
    LEFT JOIN pharmatransactions pt ON d.transaction_id = pt.transaction_id
    LEFT JOIN patient p ON pt.patientid = p.patientid
    LEFT JOIN doctors doc ON d.doctorid = doc.doctorid
    WHERE psl.item_no = ?
)
SELECT * FROM RunningBalance
GROUP BY transaction_date, item_description, unitmeasure, categorydesc,
         lot_no, expiry_date, unit_cost, selling_price, qty_received,
         cris_no, beg_balance, date_received, ics_no, supplier,
         patientname, patient_type, doctorname, quantity_issued, running_balance
ORDER BY transaction_date ASC";

$stmt = $conn->prepare($sql);
$stmt->execute([$item_no]);
$utilization = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Check if data exists
if (empty($utilization)) {
    header('Content-Type: text/html; charset=utf-8');
    echo '<div style="text-align: center; margin-top: 50px; font-family: Arial, sans-serif;">';
    echo '<h2 style="color: #dc3545;">No Utilization Data Found</h2>';
    echo '<p>No utilization data found for the selected item.</p>';
    echo '<a href="javascript:history.back()" style="text-decoration: none; background-color: #007bff; color: white; padding: 10px 20px; border-radius: 5px;">Go Back</a>';
    echo '</div>';
    exit;
}

// Create PDF
$pdf = new FPDF('P', 'mm', 'A4');
$pdf->AddPage();
$pdf->SetMargins(10, 10, 10);

// Add header with logos
$pdf->Image('../images/pgns.png', 20, 10, 25);
$pdf->Image('../images/bdh.png', 170, 10, 25);

// Add title and hospital information
$pdf->SetFont('Arial', '', 12);
$pdf->Cell(0, 6, 'Republic of the Philippines', 0, 1, 'C');
$pdf->Cell(0, 6, 'Province of Northern Samar', 0, 1, 'C');
$pdf->Cell(0, 6, 'Provincial Health Office', 0, 1, 'C');
$pdf->SetFont('Arial', 'B', 16);
$pdf->Cell(0, 8, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
$pdf->SetFont('Arial', 'I', 11);
$pdf->Cell(0, 6, 'Biri Northern Samar', 0, 1, 'C');
$pdf->SetFont('Arial', 'B', 14);
$pdf->Cell(0, 8, 'PHARMACY STOCK LEDGER', 0, 1, 'C');
$pdf->Ln(5);

// Section Headers Style
$sectionHeaderStyle = function($pdf, $title) {
    $pdf->SetFillColor(28, 58, 95);
    $pdf->SetTextColor(255);
    $pdf->SetFont('Arial', 'B', 11);
    $pdf->Cell(190, 7, $title, 0, 1, 'L', true);
    $pdf->SetTextColor(0);
    $pdf->SetFont('Arial', '', 10);
    $pdf->Ln(2);
};

// Item Information Section
$sectionHeaderStyle($pdf, ' Item Information');
$pdf->SetDrawColor(200, 200, 200);

// Two-column layout for item details
$leftWidth = 95;
$rightWidth = 95;

$pdf->Cell($leftWidth, 6, 'Item Name: ' . $utilization[0]['item_description'] . ' (Item No. ' . $item_no . ')', 1, 0, 'L', false);
$pdf->Cell($rightWidth, 6, 'Category: ' . $utilization[0]['categorydesc'], 1, 1, 'L', false);
$pdf->Cell($leftWidth, 6, 'Unit of Measure: ' . $utilization[0]['unitmeasure'], 1, 0, 'L', false);
$pdf->Cell($rightWidth, 6, 'Lot Number: ' . $utilization[0]['lot_no'], 1, 1, 'L', false);

$pdf->Ln(5);

// Stock Information Section
$sectionHeaderStyle($pdf, ' Stock Information');

$pdf->Cell($leftWidth, 6, 'Expiry Date: ' . date('M d, Y', strtotime($utilization[0]['expiry_date'])), 1, 0, 'L', false);
$pdf->Cell($rightWidth, 6, 'Unit Cost: PHP ' . number_format($utilization[0]['unit_cost'], 2), 1, 1, 'L', false);
$pdf->Cell($leftWidth, 6, 'Selling Price: PHP ' . number_format($utilization[0]['selling_price'], 2), 1, 0, 'L', false);
$pdf->Cell($rightWidth, 6, 'Qty Received: ' . $utilization[0]['qty_received'], 1, 1, 'L', false);

$pdf->Ln(5);

// Reference Information Section
$sectionHeaderStyle($pdf, ' Reference Information');

$pdf->Cell($leftWidth, 6, 'CRIS No: ' . $utilization[0]['cris_no'], 1, 0, 'L', false);
$pdf->Cell($rightWidth, 6, 'ICS No: ' . $utilization[0]['ics_no'], 1, 1, 'L', false);
$pdf->Cell($leftWidth, 6, 'Date Received: ' . date('M d, Y', strtotime($utilization[0]['date_received'])), 1, 0, 'L', false);
$pdf->Cell($rightWidth, 6, 'Supplier: ' . $utilization[0]['supplier'], 1, 1, 'L', false);
$pdf->Cell(190, 6, 'Beginning Balance: ' . $utilization[0]['beg_balance'], 1, 1, 'L', false);

// Utilization table
$pdf->Ln(5);
$pdf->SetFont('Arial', 'B', 10);
$pdf->Cell(0, 5, 'Utilization Details', 0, 1);

// Table headers with improved styling
$pdf->SetFillColor(240, 240, 240);
$pdf->Cell(40, 8, 'Date', 1, 0, 'C', true);
$pdf->Cell(60, 8, 'Doctor', 1, 0, 'C', true);
$pdf->Cell(60, 8, 'Patient Name', 1, 0, 'C', true);
$pdf->Cell(15, 8, 'Qty', 1, 0, 'C', true);
$pdf->Cell(15, 8, 'Balance', 1, 1, 'C', true);

// Table content
$pdf->SetFont('Arial', '', 9);
$total_quantity = 0;

foreach ($utilization as $row) {
    $pdf->Cell(40, 6, date('M d, Y', strtotime($row['transaction_date'])), 1, 0, 'L');
    $pdf->Cell(60, 6, $row['doctorname'], 1, 0, 'L');
    $pdf->Cell(60, 6, $row['patientname'], 1, 0, 'L');
    $pdf->Cell(15, 6, number_format($row['quantity_issued'], 0), 1, 0, 'R');
    $pdf->Cell(15, 6, number_format($row['running_balance'], 0), 1, 1, 'R');
    $total_quantity += $row['quantity_issued'];
}

// Print total with styling
$pdf->SetFont('Arial', 'B', 10);
$pdf->Cell(160, 8, 'Total Quantity Used:', 1, 0, 'R', true); // 160 = 40 + 60 + 60 + 15 + 15
$pdf->Cell(0, 8, number_format($total_quantity, 0), 1, 1, 'C', true);

$pdf->Ln(5);

// Output PDF
$pdf->Output('I', 'item_utilization_' . $item_no . '.pdf');
