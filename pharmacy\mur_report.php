<?php
require_once '../database.php';
require_once '../fpdf/fpdf.php';

// Get selected month and year or default to current month
$selectedMonth = isset($_GET['month']) ? $_GET['month'] : date('Y-m');
$startDate = $selectedMonth . '-01';
$endDate = date('Y-m-t', strtotime($startDate));

// Get previous month
$prevMonth = date('Y-m', strtotime($startDate . ' -1 month'));
$prevMonthStart = $prevMonth . '-01';
$prevMonthEnd = date('Y-m-t', strtotime($prevMonthStart));

// Main SQL query for fetching item data
$sql = "
SELECT 
    i.itemid,
    i.generaldescription AS item_name,
    i.unitmeasure,
    psl.item_no,
    psl.cris_no,
    psl.unit_cost,

    -- Beginning balance (received - dispensed - returned before prevMonthEnd)
    COALESCE(
        (
            SELECT JSON_OBJECT(
                'qty', 
                    COALESCE(SUM(CASE WHEN psl2.date_received <= ? THEN psl2.qty_received ELSE 0 END), 0)
                    - COALESCE(SUM(CASE WHEN t.transaction_date <= ? THEN ptd2.quantity ELSE 0 END), 0)
                    - COALESCE(SUM(CASE WHEN r.date_returned <= ? THEN r.quantity_returned ELSE 0 END), 0),
                'unit_cost', psl.unit_cost
            )
            FROM pharmacy_stock_ledger psl2
            LEFT JOIN pharmatransaction_details ptd2 ON psl2.item_no = ptd2.item_no
            LEFT JOIN pharmatransactions t ON ptd2.transaction_id = t.transaction_id
            LEFT JOIN return_to_supplier r ON psl2.itemid = r.itemid
            WHERE psl2.itemid = i.itemid
            GROUP BY psl2.itemid
        ),
        JSON_OBJECT('qty', 0, 'unit_cost', 0)
    ) AS beginning_data,

    -- Received data (selected month)
    COALESCE(
        (
            SELECT JSON_OBJECT(
                'qty', SUM(psl2.qty_received),
                'unit_cost', psl.unit_cost
            )
            FROM pharmacy_stock_ledger psl2
            WHERE psl2.itemid = i.itemid
              AND psl2.date_received BETWEEN ? AND ?
            GROUP BY psl2.itemid
        ),
        JSON_OBJECT('qty', 0, 'unit_cost', 0)
    ) AS received_data,

    -- Dispensed data (selected month)
    COALESCE(
        (
            SELECT JSON_OBJECT(
                'qty', SUM(ptd2.quantity),
                'unit_cost', psl.unit_cost
            )
            FROM pharmatransactions t
            JOIN pharmatransaction_details ptd2 ON t.transaction_id = ptd2.transaction_id
            JOIN pharmacy_stock_ledger psl2 ON ptd2.item_no = psl2.item_no
            WHERE psl2.itemid = i.itemid
              AND t.transaction_date BETWEEN ? AND ?
            GROUP BY psl2.itemid
        ),
        JSON_OBJECT('qty', 0, 'unit_cost', 0)
    ) AS dispensed_data,

    -- Returned data (selected month)
    COALESCE(
        (
            SELECT JSON_OBJECT(
                'qty', SUM(r.quantity_returned),
                'unit_cost', psl.unit_cost
            )
            FROM return_to_supplier r
            JOIN pharmacy_stock_ledger psl2 ON r.itemid = psl2.itemid
            WHERE r.itemid = i.itemid
              AND r.date_returned BETWEEN ? AND ?
            GROUP BY r.itemid
        ),
        JSON_OBJECT('qty', 0, 'unit_cost', 0)
    ) AS returned_data

FROM items i
JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid
WHERE psl.item_no IS NOT NULL
GROUP BY i.itemid, i.generaldescription, i.unitmeasure, psl.item_no, psl.cris_no, psl.unit_cost
ORDER BY psl.item_no ASC;
";

// Prepare and execute the query
$stmt = $conn->prepare($sql);
try {
    $params = [
        $prevMonthEnd, // Beginning balance date params
        $prevMonthEnd,
        $prevMonthEnd,
        $startDate,    // Current month date ranges
        $endDate,
        $startDate,
        $endDate,
        $startDate,
        $endDate
    ];
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Convert JSON strings to arrays and handle potential JSON errors
    foreach ($results as &$row) {
        $row['beginning_data'] = json_decode($row['beginning_data'], true) ?: ['qty' => 0, 'unit_cost' => 0];
        $row['received_data'] = json_decode($row['received_data'], true) ?: ['qty' => 0, 'unit_cost' => 0];
        $row['dispensed_data'] = json_decode($row['dispensed_data'], true) ?: ['qty' => 0, 'unit_cost' => 0];
        $row['returned_data'] = json_decode($row['returned_data'], true) ?: ['qty' => 0, 'unit_cost' => 0];
    }
} catch (PDOException $e) {
    error_log("SQL Error in MUR Report: " . $e->getMessage());
    die("An error occurred while generating the report. Please check the error log.");
}

// Calculate ending balance and totals with validation
foreach ($results as &$row) {
    // Ensure numeric values and handle potential null/invalid values
    $beginning_qty = floatval($row['beginning_data']['qty']);
    $beginning_cost = floatval($row['beginning_data']['unit_cost']);
    $beginning_total = $beginning_qty * $beginning_cost;

    $received_qty = floatval($row['received_data']['qty']);
    $received_cost = floatval($row['received_data']['unit_cost']);
    $received_total = $received_qty * $received_cost;

    $dispensed_qty = floatval($row['dispensed_data']['qty']);
    $dispensed_cost = floatval($row['dispensed_data']['unit_cost']);
    $dispensed_total = $dispensed_qty * $dispensed_cost;

    $returned_qty = floatval($row['returned_data']['qty']);
    $returned_cost = floatval($row['returned_data']['unit_cost']);
    $returned_total = $returned_qty * $returned_cost;

    // ✅ Calculate ending balance with fallback cost logic
    $ending_qty = $beginning_qty + $received_qty - $dispensed_qty - $returned_qty;
    $ending_cost = $beginning_cost > 0 ? $beginning_cost : ($received_cost > 0 ? $received_cost : 0);
    $ending_total = $ending_qty * $ending_cost;

    // ✅ Store calculated balances
    $row['balances'] = [
        'beginning' => ['qty' => $beginning_qty, 'cost' => $beginning_cost, 'total' => $beginning_total],
        'received'  => ['qty' => $received_qty,  'cost' => $received_cost,  'total' => $received_total],
        'dispensed' => ['qty' => $dispensed_qty, 'cost' => $dispensed_cost, 'total' => $dispensed_total],
        'returned'  => ['qty' => $returned_qty,  'cost' => $returned_cost,  'total' => $returned_total],
        'ending'    => ['qty' => $ending_qty,    'cost' => $ending_cost,    'total' => $ending_total]
    ];
}
unset($row);


class PDF extends FPDF {
    function Header() {
        // Add logos
        $this->Image('../images/pgns.png', 90, 10, 25);
        $this->Image('../images/bdh.png', 215, 10, 25);

        // Add title and header information
        $this->SetFont('Arial', '', 9);
        $this->Cell(0, 4, 'Republic of the Philippines', 0, 1, 'C');
        $this->Cell(0, 4, 'PROVINCIAL GOVERNMENT OF NORTHERN SAMAR', 0, 1, 'C');
        $this->Cell(0, 4, 'PROVINCIAL HEALTH OFFICE', 0, 1, 'C');
        $this->Cell(0, 4, 'PRO-HEALTH CONSIGNMENT MANAGEMENT COMMITTEE (CMC)', 0, 1, 'C');
        
         $this->Ln(5);
        $this->SetFont('Arial', 'B', 12);
        $this->Cell(0, 4, 'MONTHLY UTILIZATIONS REPORT', 0, 1, 'C');
          $this->SetFont('Arial', 'B', 8);
        $this->Cell(0, 4, 'DRUGS, MEDICINES AND MEDICAL SUPPLIES UNDER CONSIGNMENT', 0, 1, 'C');
        $this->Cell(0, 4, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
        
        $this->SetFont('Arial', '', 10);
        $this->Cell(0, 6, 'For the month of ' . date('F Y', strtotime($GLOBALS['selectedMonth'])), 0, 1, 'C');
        $this->Ln(5);
    }
}

function safe_balance($value) {
    if (is_array($value)) {
        return [
            'qty' => $value['qty'] ?? 0,
            'unit_cost' => $value['unit_cost'] ?? 0
        ];
    }

    if (is_string($value)) {
        $decoded = json_decode($value, true);
        if (is_array($decoded)) {
            return [
                'qty' => $decoded['qty'] ?? 0,
                'unit_cost' => $decoded['unit_cost'] ?? 0
            ];
        }
    }

    return ['qty' => 0, 'unit_cost' => 0];
}

foreach ($results as &$row) {
    $beginning = safe_balance($row['beginning_data']);
    $received  = safe_balance($row['received_data']);
    $dispensed = safe_balance($row['dispensed_data']);
    $returned  = safe_balance($row['returned_data']);

    $ending_qty = $beginning['qty'] + $received['qty'] - $dispensed['qty'] - $returned['qty'];
    $ending_cost = $beginning['unit_cost'];

    $row['balances'] = [
        'beginning' => $beginning,
        'received'  => $received,
        'dispensed' => $dispensed,
        'returned'  => $returned,
        'ending'    => [
            'qty' => $ending_qty,
            'unit_cost' => $ending_cost
        ]
    ];

    foreach ($row['balances'] as &$group) {
        $group['total'] = $group['qty'] * $group['unit_cost'];
        $group['cost'] = $group['unit_cost'];
    }
}
unset($row);


// Initialize PDF
$pdf = new PDF('L', 'mm', array(215.9, 330.2));
$pdf->AddPage();
$pdf->SetMargins(10, 10, 10);
$pdf->SetAutoPageBreak(true, 10);

// Header colors
$headerBlue = array(200, 220, 255);
$headerPink = array(255, 200, 220);

// Define data keys and their printable labels
$groupKeys = ['beginning', 'received', 'dispensed', 'returned', 'ending'];
$groupLabels = [
    'beginning' => 'Beginning Balance',
    'received' => 'Replenishments',
    'dispensed' => 'Utilizations',
    'returned' => 'Returns to Warehouse',
    'ending' => 'Ending Balance'
];

// Table Headers
$pdf->SetFillColor(...$headerBlue);
$pdf->SetFont('Arial', 'B', 9);
$pdf->Cell(10, 10, 'No.', 1, 0, 'C', true);
$pdf->Cell(60, 10, 'Item Name (CRIS No.)', 1, 0, 'C', true);
$pdf->Cell(15, 10, 'Unit', 1, 0, 'C', true);

$pdf->SetFillColor(...$headerPink);
foreach ($groupKeys as $key) {
    $pdf->Cell(45, 5, $groupLabels[$key], 1, 0, 'C', true);
}
$pdf->Ln();

// Subheaders
$pdf->SetFillColor(...$headerBlue);
$pdf->Cell(85, 5, '', 0, 0);
for ($i = 0; $i < count($groupKeys); $i++) {
    $pdf->Cell(15, 5, 'Qty', 1, 0, 'C', true);
    $pdf->Cell(15, 5, 'Unit Cost', 1, 0, 'C', true);
    $pdf->Cell(15, 5, 'Total', 1, 0, 'C', true);
}
$pdf->Ln();

// Initialize grand totals
$grand_totals = [];
foreach ($groupKeys as $key) {
    $grand_totals[$key] = ['qty' => 0, 'cost' => 0, 'total' => 0];
}

// Data Rows
$counter = 1;
$rowColor = [245, 245, 245];

foreach ($results as $row) {
    // Handle page break
    if ($pdf->GetY() > 180) {
        $pdf->AddPage();

        // Reprint headers
        $pdf->SetFillColor(...$headerBlue);
        $pdf->SetFont('Arial', 'B', 9);
        $pdf->Cell(10, 10, 'No.', 1, 0, 'C', true);
        $pdf->Cell(60, 10, 'Item Name (CRIS No.)', 1, 0, 'C', true);
        $pdf->Cell(15, 10, 'Unit', 1, 0, 'C', true);

        $pdf->SetFillColor(...$headerPink);
        foreach ($groupKeys as $key) {
            $pdf->Cell(45, 5, $groupLabels[$key], 1, 0, 'C', true);
        }
        $pdf->Ln();

        // Subheaders
        $pdf->SetFillColor(...$headerBlue);
        $pdf->Cell(85, 5, '', 0, 0);
        for ($i = 0; $i < count($groupKeys); $i++) {
            $pdf->Cell(15, 5, 'Qty', 1, 0, 'C', true);
            $pdf->Cell(15, 5, 'Unit Cost', 1, 0, 'C', true);
            $pdf->Cell(15, 5, 'Total', 1, 0, 'C', true);
        }
        $pdf->Ln();
    }

    // Row output
    $pdf->SetFillColor(...$rowColor);
    $pdf->SetFont('Arial', '', 8);

    $pdf->Cell(10, 5, $row['item_no'], 1, 0, 'C', ($counter % 2 == 0));
    $pdf->Cell(60, 5, $row['item_name'] . ' (' . $row['cris_no'] . ')', 1, 0, 'L', ($counter % 2 == 0));
    $pdf->Cell(15, 5, $row['unitmeasure'], 1, 0, 'C', ($counter % 2 == 0));

  $groupKeys = ['beginning', 'received', 'dispensed', 'returned', 'ending'];

foreach ($groupKeys as $group) {
    $balance = $row['balances'][$group] ?? ['qty' => 0, 'cost' => 0, 'total' => 0];

    // 🛡 Ensure valid array structure
    if (is_string($balance)) {
        $balance = json_decode($balance, true);
    }
    if (!is_array($balance)) {
        $balance = ['qty' => 0, 'cost' => 0, 'total' => 0];
    }

    // Recalculate ending balance explicitly if this is the "ending" group
    if ($group === 'ending') {
        $beginning_qty = $row['balances']['beginning']['qty'] ?? 0;
        $received_qty  = $row['balances']['received']['qty'] ?? 0;
        $dispensed_qty = $row['balances']['dispensed']['qty'] ?? 0;
        $returned_qty  = $row['balances']['returned']['qty'] ?? 0;

        $ending_qty = $beginning_qty + $received_qty - $dispensed_qty - $returned_qty;
        $ending_cost = $row['beginning_data']['unit_cost'] ?? 0; // ✅ Your formula
        $ending_total = $ending_qty * $ending_cost;

        $balance = [
            'qty' => $ending_qty,
            'cost' => $ending_cost,
            'total' => $ending_total
        ];
    }

    // Accumulate grand totals safely
    $grand_totals[$group]['qty'] += $balance['qty'];
    $grand_totals[$group]['cost'] += $balance['cost'];
    $grand_totals[$group]['total'] += $balance['total'];

    // Output to PDF
    $pdf->Cell(15, 5, number_format($balance['qty'], 2), 1, 0, 'R', ($counter % 2 == 0));
    $pdf->Cell(15, 5, number_format($balance['cost'], 2), 1, 0, 'R', ($counter % 2 == 0));
    $pdf->Cell(15, 5, number_format($balance['total'], 2), 1, 0, 'R', ($counter % 2 == 0));
}


    $pdf->Ln();
    $counter++;
}

// Grand Totals Row
$pdf->SetFillColor(220, 220, 220);
$pdf->SetFont('Arial', 'B', 9);
$pdf->Cell(10, 6, '', 1, 0, 'C', true);
$pdf->Cell(60, 6, 'GRAND TOTAL', 1, 0, 'C', true);
$pdf->Cell(15, 6, '', 1, 0, 'C', true);

foreach ($groupKeys as $group) {
    $pdf->Cell(15, 6, number_format($grand_totals[$group]['qty'], 2), 1, 0, 'R', true);
    $pdf->Cell(15, 6, '', 1, 0, 'R', true); // You can skip cost if needed
    $pdf->Cell(15, 6, number_format($grand_totals[$group]['total'], 2), 1, 0, 'R', true);
}
$pdf->Ln();

// Signatories
$pdf->Ln(20);
$pdf->SetFont('Arial', '', 10);
$pdf->Cell(110, 6, 'Prepared by:', 0, 0, 'L');
$pdf->Cell(110, 6, 'Reviewed by:', 0, 0, 'L');
$pdf->Cell(110, 6, 'Noted by:', 0, 1, 'L');

$pdf->Ln(15);
$pdf->SetFont('Arial', 'B', 10);
$pdf->Cell(110, 5, 'RONNIE B. CELIS', 0, 0, 'L');
$pdf->Cell(110, 5, 'CHARLIE JANE LEBECO', 0, 0, 'L');
$pdf->Cell(110, 5, 'LUCILLE G. ROMINES, MD, FPCP', 0, 1, 'L');

$pdf->SetFont('Arial', 'I', 10);
$pdf->Cell(110, 5, 'BDH, Pharmacist', 0, 0, 'L');
$pdf->Cell(110, 5, 'CMC, Accountant', 0, 0, 'L');
$pdf->Cell(110, 5, 'CHIEF OF HOSPITAL', 0, 1, 'L');

// Output PDF
$pdf->Output('I', 'Monthly_Utilization_Report.pdf');
