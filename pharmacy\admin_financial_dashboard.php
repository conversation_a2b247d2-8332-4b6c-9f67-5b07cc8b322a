<?php
require_once '../database.php';

// Get date filters
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01'); // First day of current month
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d'); // Today
$period_type = isset($_GET['period_type']) ? $_GET['period_type'] : 'daily';

// 1. Revenue Analysis
$revenue_sql = "SELECT
    DATE(pt.transaction_date) as date,
    COUNT(DISTINCT pt.transaction_id) as transaction_count,
    SUM(ptd.subtotal) as daily_revenue,
    SUM(ptd.original_price * ptd.quantity) as gross_revenue,
    SUM(ptd.discount_applied) as total_discounts,
    AVG(transaction_totals.transaction_total) as avg_transaction_value
FROM pharmatransactions pt
JOIN pharmatransaction_details ptd ON pt.transaction_id = ptd.transaction_id
JOIN (
    SELECT transaction_id, SUM(subtotal) as transaction_total
    FROM pharmatransaction_details
    GROUP BY transaction_id
) transaction_totals ON pt.transaction_id = transaction_totals.transaction_id
WHERE pt.transaction_date BETWEEN ? AND ?
GROUP BY DATE(pt.transaction_date)
ORDER BY date DESC";

$revenue_stmt = $conn->prepare($revenue_sql);
$revenue_stmt->execute([$start_date, $end_date]);
$revenue_data = $revenue_stmt->fetchAll(PDO::FETCH_ASSOC);

// 2. Cost vs Revenue Analysis
$cost_revenue_sql = "SELECT 
    i.generaldescription,
    i.itemid,
    SUM(ptd.quantity) as total_sold,
    SUM(ptd.subtotal) as revenue,
    AVG(psl.unit_cost) as avg_cost,
    SUM(ptd.quantity * psl.unit_cost) as total_cost,
    (SUM(ptd.subtotal) - SUM(ptd.quantity * psl.unit_cost)) as profit,
    ((SUM(ptd.subtotal) - SUM(ptd.quantity * psl.unit_cost)) / SUM(ptd.subtotal) * 100) as profit_margin
FROM pharmatransactions pt
JOIN pharmatransaction_details ptd ON pt.transaction_id = ptd.transaction_id
JOIN items i ON ptd.itemid = i.itemid
JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid
WHERE pt.transaction_date BETWEEN ? AND ?
GROUP BY i.itemid, i.generaldescription
HAVING total_sold > 0
ORDER BY profit DESC
LIMIT 20";

$cost_revenue_stmt = $conn->prepare($cost_revenue_sql);
$cost_revenue_stmt->execute([$start_date, $end_date]);
$cost_revenue_data = $cost_revenue_stmt->fetchAll(PDO::FETCH_ASSOC);

// 3. Payment Method Analysis using actual payment table
$payment_sql = "SELECT
    p.payment_type,
    COUNT(*) as transaction_count,
    SUM(p.amount_paid) as total_amount,
    AVG(p.amount_paid) as avg_amount
FROM payment p
JOIN pharmatransactions pt ON p.transaction_id = pt.transaction_id
WHERE pt.transaction_date BETWEEN ? AND ?
GROUP BY p.payment_type
ORDER BY total_amount DESC";

$payment_stmt = $conn->prepare($payment_sql);
$payment_stmt->execute([$start_date, $end_date]);
$payment_data = $payment_stmt->fetchAll(PDO::FETCH_ASSOC);

// 4. Outstanding Payments (transactions without payments or partial payments)
$outstanding_sql = "SELECT
    pt.transaction_reference,
    pt.transaction_date,
    pat.patientname,
    transaction_totals.total_amount,
    COALESCE(payment_totals.amount_paid, 0) as amount_paid,
    (transaction_totals.total_amount - COALESCE(payment_totals.amount_paid, 0)) as outstanding_amount,
    DATEDIFF(CURDATE(), pt.transaction_date) as days_outstanding
FROM pharmatransactions pt
JOIN patient pat ON pt.patientid = pat.patientid
JOIN (
    SELECT transaction_id, SUM(subtotal) as total_amount
    FROM pharmatransaction_details
    GROUP BY transaction_id
) transaction_totals ON pt.transaction_id = transaction_totals.transaction_id
LEFT JOIN (
    SELECT transaction_id, SUM(amount_paid) as amount_paid
    FROM payment
    GROUP BY transaction_id
) payment_totals ON pt.transaction_id = payment_totals.transaction_id
WHERE (transaction_totals.total_amount - COALESCE(payment_totals.amount_paid, 0)) > 0
AND pt.transaction_date BETWEEN ? AND ?
ORDER BY days_outstanding DESC, outstanding_amount DESC";

$outstanding_stmt = $conn->prepare($outstanding_sql);
$outstanding_stmt->execute([$start_date, $end_date]);
$outstanding_data = $outstanding_stmt->fetchAll(PDO::FETCH_ASSOC);

// 5. Summary Statistics
$summary_sql = "SELECT
    COUNT(DISTINCT pt.transaction_id) as total_transactions,
    COUNT(DISTINCT pt.patientid) as unique_patients,
    SUM(ptd.subtotal) as total_revenue,
    AVG(transaction_totals.transaction_total) as avg_transaction_value,
    SUM(ptd.discount_applied) as total_discounts,
    COUNT(DISTINCT ptd.itemid) as items_sold
FROM pharmatransactions pt
JOIN pharmatransaction_details ptd ON pt.transaction_id = ptd.transaction_id
JOIN (
    SELECT transaction_id, SUM(subtotal) as transaction_total
    FROM pharmatransaction_details
    GROUP BY transaction_id
) transaction_totals ON pt.transaction_id = transaction_totals.transaction_id
WHERE pt.transaction_date BETWEEN ? AND ?";

$summary_stmt = $conn->prepare($summary_sql);
$summary_stmt->execute([$start_date, $end_date]);
$summary_data = $summary_stmt->fetch(PDO::FETCH_ASSOC);

// 6. Top Performing Categories
$category_sql = "SELECT 
    pc.categorydesc,
    COUNT(DISTINCT ptd.itemid) as items_count,
    SUM(ptd.quantity) as total_quantity,
    SUM(ptd.subtotal) as category_revenue,
    AVG(ptd.unit_price) as avg_price
FROM pharmatransactions pt
JOIN pharmatransaction_details ptd ON pt.transaction_id = ptd.transaction_id
JOIN items i ON ptd.itemid = i.itemid
JOIN pharmacategory pc ON i.category = pc.categoryid
WHERE pt.transaction_date BETWEEN ? AND ?
GROUP BY pc.categoryid, pc.categorydesc
ORDER BY category_revenue DESC";

$category_stmt = $conn->prepare($category_sql);
$category_stmt->execute([$start_date, $end_date]);
$category_data = $category_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Financial Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #ecf0f1;
            --white: #ffffff;
            --text-dark: #2c3e50;
            --text-muted: #7f8c8d;
            --border-color: #bdc3c7;
            --shadow-light: 0 2px 10px rgba(44, 62, 80, 0.1);
            --shadow-medium: 0 4px 20px rgba(44, 62, 80, 0.15);
            --shadow-heavy: 0 8px 30px rgba(44, 62, 80, 0.2);
        }

        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-dark);
        }

        .dashboard-card {
            border: none;
            border-radius: 12px;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            background: var(--white);
            border-left: 4px solid var(--accent-color);
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .stat-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-medium);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .stat-value {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 2;
        }

        .stat-label {
            font-size: 0.85rem;
            opacity: 0.9;
            font-weight: 500;
            position: relative;
            z-index: 2;
        }

        .chart-container {
            position: relative;
            height: 380px;
            margin-bottom: 20px;
            padding: 20px;
            background: var(--white);
            border-radius: 8px;
        }

        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--shadow-light);
        }

        .table {
            margin-bottom: 0;
        }

        .table thead th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 16px 12px;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: rgba(52, 152, 219, 0.05);
            transform: scale(1.01);
        }

        .table tbody td {
            padding: 14px 12px;
            border-color: var(--border-color);
            font-size: 0.9rem;
        }

        .btn-export {
            background: linear-gradient(135deg, var(--accent-color) 0%, #2980b9 100%);
            border: none;
            color: white;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-light);
        }

        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
            color: white;
            background: linear-gradient(135deg, #2980b9 0%, var(--accent-color) 100%);
        }

        .card-header {
            background: linear-gradient(135deg, var(--white) 0%, #f8f9fa 100%);
            border-bottom: 2px solid var(--accent-color);
            border-radius: 12px 12px 0 0 !important;
            padding: 20px 24px;
        }

        .card-header h5 {
            color: var(--primary-color);
            font-weight: 700;
            margin: 0;
            font-size: 1.1rem;
        }

        .badge {
            font-weight: 600;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
        }

        .badge.bg-success {
            background: var(--success-color) !important;
        }

        .badge.bg-warning {
            background: var(--warning-color) !important;
        }

        .badge.bg-danger {
            background: var(--danger-color) !important;
        }

        .badge.bg-info {
            background: var(--accent-color) !important;
        }

        .badge.bg-primary {
            background: var(--primary-color) !important;
        }

        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-primary {
            background: var(--accent-color);
            border-color: var(--accent-color);
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: #2980b9;
            border-color: #2980b9;
            transform: translateY(-1px);
        }

        .text-success { color: var(--success-color) !important; }
        .text-danger { color: var(--danger-color) !important; }
        .text-primary { color: var(--accent-color) !important; }
        .text-muted { color: var(--text-muted) !important; }

        /* Modern scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--light-bg);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--text-muted);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .stat-value {
                font-size: 1.8rem;
            }

            .chart-container {
                height: 300px;
                padding: 15px;
            }

            .dashboard-card {
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="mb-0">
                                    <i class="fas fa-chart-line me-3 text-primary"></i>
                                    Admin Financial Dashboard
                                </h2>
                                <p class="text-muted mb-0">Comprehensive financial analytics and insights</p>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-export" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf me-2"></i>Export PDF
                                </button>
                                <button class="btn btn-export" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel me-2"></i>Export Excel
                                </button>
                                <a href="../pharmacy/pharmacydashboard.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-home me-2"></i>Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Date Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card card">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">
                                    <i class="fas fa-calendar-alt me-2"></i>Start Date
                                </label>
                                <input type="date" class="form-control" name="start_date" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">
                                    <i class="fas fa-calendar-alt me-2"></i>End Date
                                </label>
                                <input type="date" class="form-control" name="end_date" value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">
                                    <i class="fas fa-chart-bar me-2"></i>Period Type
                                </label>
                                <select class="form-select" name="period_type">
                                    <option value="daily" <?php echo $period_type === 'daily' ? 'selected' : ''; ?>>Daily</option>
                                    <option value="weekly" <?php echo $period_type === 'weekly' ? 'selected' : ''; ?>>Weekly</option>
                                    <option value="monthly" <?php echo $period_type === 'monthly' ? 'selected' : ''; ?>>Monthly</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter me-2"></i>Apply Filter
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-value">₱<?php echo number_format($summary_data['total_revenue'] ?? 0, 2); ?></div>
                    <div class="stat-label">Total Revenue</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-value"><?php echo number_format($summary_data['total_transactions'] ?? 0); ?></div>
                    <div class="stat-label">Total Transactions</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-value"><?php echo number_format($summary_data['unique_patients'] ?? 0); ?></div>
                    <div class="stat-label">Unique Patients</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-value">₱<?php echo number_format($summary_data['avg_transaction_value'] ?? 0, 2); ?></div>
                    <div class="stat-label">Avg Transaction</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-value">₱<?php echo number_format($summary_data['total_discounts'] ?? 0, 2); ?></div>
                    <div class="stat-label">Total Discounts</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card text-center">
                    <div class="stat-value"><?php echo number_format($summary_data['items_sold'] ?? 0); ?></div>
                    <div class="stat-label">Items Sold</div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <!-- Revenue Trend Chart -->
            <div class="col-md-8">
                <div class="dashboard-card card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>Revenue Trend
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Methods Chart -->
            <div class="col-md-4">
                <div class="dashboard-card card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-credit-card me-2"></i>Payment Methods
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="paymentChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing Items -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="dashboard-card card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy me-2"></i>Top Profitable Items
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Item</th>
                                        <th>Sold</th>
                                        <th>Revenue</th>
                                        <th>Profit</th>
                                        <th>Margin %</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach(array_slice($cost_revenue_data, 0, 10) as $item): ?>
                                    <tr>
                                        <td>
                                            <small><?php echo htmlspecialchars($item['generaldescription']); ?></small>
                                        </td>
                                        <td><?php echo number_format($item['total_sold']); ?></td>
                                        <td>₱<?php echo number_format($item['revenue'], 2); ?></td>
                                        <td class="<?php echo $item['profit'] > 0 ? 'text-success' : 'text-danger'; ?>">
                                            ₱<?php echo number_format($item['profit'], 2); ?>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $item['profit_margin'] > 20 ? 'bg-success' : ($item['profit_margin'] > 10 ? 'bg-warning' : 'bg-danger'); ?>">
                                                <?php echo number_format($item['profit_margin'], 1); ?>%
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Category Performance -->
            <div class="col-md-6">
                <div class="dashboard-card card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tags me-2"></i>Category Performance
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Category</th>
                                        <th>Items</th>
                                        <th>Quantity</th>
                                        <th>Revenue</th>
                                        <th>Avg Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($category_data as $category): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($category['categorydesc']); ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $category['items_count']; ?></span>
                                        </td>
                                        <td><?php echo number_format($category['total_quantity']); ?></td>
                                        <td>₱<?php echo number_format($category['category_revenue'], 2); ?></td>
                                        <td>₱<?php echo number_format($category['avg_price'], 2); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outstanding Payments -->
        <?php if (!empty($outstanding_data)): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2 text-warning"></i>Outstanding Payments
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Reference</th>
                                        <th>Date</th>
                                        <th>Patient</th>
                                        <th>Total Amount</th>
                                        <th>Paid</th>
                                        <th>Outstanding</th>
                                        <th>Days</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach(array_slice($outstanding_data, 0, 15) as $payment): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($payment['transaction_reference']); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($payment['transaction_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($payment['patientname']); ?></td>
                                        <td>₱<?php echo number_format($payment['total_amount'], 2); ?></td>
                                        <td>₱<?php echo number_format($payment['amount_paid'], 2); ?></td>
                                        <td class="text-danger fw-bold">₱<?php echo number_format($payment['outstanding_amount'], 2); ?></td>
                                        <td>
                                            <span class="badge <?php echo $payment['days_outstanding'] > 30 ? 'bg-danger' : ($payment['days_outstanding'] > 7 ? 'bg-warning' : 'bg-info'); ?>">
                                                <?php echo $payment['days_outstanding']; ?> days
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($payment['days_outstanding'] > 30): ?>
                                                <span class="badge bg-danger">Overdue</span>
                                            <?php elseif ($payment['days_outstanding'] > 7): ?>
                                                <span class="badge bg-warning">Due Soon</span>
                                            <?php else: ?>
                                                <span class="badge bg-success">Current</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Revenue Trend Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');

        // Create gradient
        const gradient = revenueCtx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, 'rgba(52, 152, 219, 0.3)');
        gradient.addColorStop(1, 'rgba(52, 152, 219, 0.05)');

        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: [
                    <?php foreach(array_reverse($revenue_data) as $day): ?>
                        '<?php echo date('M d', strtotime($day['date'])); ?>',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    label: 'Daily Revenue',
                    data: [
                        <?php foreach(array_reverse($revenue_data) as $day): ?>
                            <?php echo $day['daily_revenue']; ?>,
                        <?php endforeach; ?>
                    ],
                    borderColor: '#3498db',
                    backgroundColor: gradient,
                    borderWidth: 3,
                    pointBackgroundColor: '#2c3e50',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(44, 62, 80, 0.9)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#3498db',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return 'Revenue: ₱' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#7f8c8d',
                            font: {
                                size: 12,
                                weight: '500'
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(189, 195, 199, 0.3)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#7f8c8d',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            callback: function(value) {
                                return '₱' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Payment Methods Chart
        const paymentCtx = document.getElementById('paymentChart').getContext('2d');
        const paymentChart = new Chart(paymentCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    <?php foreach($payment_data as $payment): ?>
                        '<?php echo $payment['payment_type']; ?>',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    data: [
                        <?php foreach($payment_data as $payment): ?>
                            <?php echo $payment['total_amount']; ?>,
                        <?php endforeach; ?>
                    ],
                    backgroundColor: [
                        '#2c3e50',  // Dark blue-gray
                        '#3498db',  // Professional blue
                        '#27ae60',  // Professional green
                        '#f39c12',  // Professional orange
                        '#e74c3c',  // Professional red
                        '#9b59b6'   // Professional purple
                    ],
                    borderWidth: 0,
                    hoverBorderWidth: 3,
                    hoverBorderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#2c3e50',
                            font: {
                                size: 12,
                                weight: '600'
                            },
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(44, 62, 80, 0.9)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#3498db',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ₱' + context.parsed.toLocaleString() + ' (' + percentage + '%)';
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 1000
                }
            }
        });

        // Export Functions
        function exportToPDF() {
            window.print();
        }

        function exportToExcel() {
            // Create a simple CSV export
            let csv = 'Item,Sold,Revenue,Profit,Margin\n';
            <?php foreach($cost_revenue_data as $item): ?>
            csv += '<?php echo addslashes($item['generaldescription']); ?>,<?php echo $item['total_sold']; ?>,<?php echo $item['revenue']; ?>,<?php echo $item['profit']; ?>,<?php echo $item['profit_margin']; ?>\n';
            <?php endforeach; ?>

            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.setAttribute('hidden', '');
            a.setAttribute('href', url);
            a.setAttribute('download', 'financial_report_<?php echo date('Y-m-d'); ?>.csv');
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }
    </script>
</body>
</html>
