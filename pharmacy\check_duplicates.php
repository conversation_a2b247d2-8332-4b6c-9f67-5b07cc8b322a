<?php
require_once '../database.php';

// Get selected month or default to current month
$selectedMonth = isset($_GET['month']) ? $_GET['month'] . '-01' : date('Y-m-01');
$selectedMonthEnd = date('Y-m-t', strtotime($selectedMonth));
$startDate = $selectedMonth;
$endDate = $selectedMonthEnd;

echo "<h2>Duplicate Items Check - Monthly Utilization Report</h2>";
echo "<p><strong>Period:</strong> " . date('F Y', strtotime($selectedMonth)) . "</p>";

// Run the same query as MUR to check for duplicates
$sql = "
WITH ItemsWithActivity AS (
    -- Get all items that have any activity (stock, transactions, or returns)
    SELECT DISTINCT i.itemid, i.generaldescription, i.unitmeasure
    FROM items i
    WHERE EXISTS (
        SELECT 1 FROM pharmacy_stock_ledger psl WHERE psl.itemid = i.itemid
    ) OR EXISTS (
        SELECT 1 FROM pharmacy_stock_ledger psl2 
        JOIN pharmatransaction_details ptd ON psl2.item_no = ptd.item_no
        JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
        WHERE psl2.itemid = i.itemid AND pt.transaction_date BETWEEN ? AND ?
    ) OR EXISTS (
        SELECT 1 FROM return_to_supplier r WHERE r.itemid = i.itemid
    )
),
BeginningBalance AS (
    -- Calculate beginning balance (everything before start date)
    SELECT 
        iwa.itemid,
        (COALESCE(received.total_received, 0) - 
         COALESCE(dispensed.total_dispensed, 0) - 
         COALESCE(returned.total_returned, 0)) as beginning_qty
    FROM ItemsWithActivity iwa
    LEFT JOIN (
        SELECT itemid, SUM(qty_received) as total_received
        FROM pharmacy_stock_ledger 
        WHERE date_received < ?
        GROUP BY itemid
    ) received ON iwa.itemid = received.itemid
    LEFT JOIN (
        SELECT psl.itemid, SUM(ptd.quantity) as total_dispensed
        FROM pharmacy_stock_ledger psl
        JOIN pharmatransaction_details ptd ON psl.item_no = ptd.item_no
        JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
        WHERE pt.transaction_date < ?
        GROUP BY psl.itemid
    ) dispensed ON iwa.itemid = dispensed.itemid
    LEFT JOIN (
        SELECT itemid, SUM(quantity_returned) as total_returned
        FROM return_to_supplier 
        WHERE date_returned < ?
        GROUP BY itemid
    ) returned ON iwa.itemid = returned.itemid
),
MonthlyReceived AS (
    -- All deliveries/receipts for the month aggregated by itemid
    SELECT 
        iwa.itemid,
        COALESCE(SUM(psl.qty_received), 0) as received_qty,
        COUNT(DISTINCT psl.date_received) as delivery_count,
        GROUP_CONCAT(DISTINCT DATE_FORMAT(psl.date_received, '%m/%d') ORDER BY psl.date_received SEPARATOR ', ') as delivery_dates,
        GROUP_CONCAT(DISTINCT psl.supplier ORDER BY psl.supplier SEPARATOR ', ') as suppliers
    FROM ItemsWithActivity iwa
    LEFT JOIN pharmacy_stock_ledger psl ON iwa.itemid = psl.itemid 
        AND psl.date_received BETWEEN ? AND ?
    GROUP BY iwa.itemid
),
MonthlyDispensed AS (
    -- All dispensing/utilization for the month aggregated by itemid
    SELECT 
        iwa.itemid,
        COALESCE(SUM(ptd.quantity), 0) as dispensed_qty,
        COUNT(DISTINCT pt.transaction_id) as transaction_count,
        COUNT(DISTINCT DATE(pt.transaction_date)) as active_days,
        MIN(DATE(pt.transaction_date)) as first_dispensed,
        MAX(DATE(pt.transaction_date)) as last_dispensed
    FROM ItemsWithActivity iwa
    LEFT JOIN pharmacy_stock_ledger psl ON iwa.itemid = psl.itemid
    LEFT JOIN pharmatransaction_details ptd ON psl.item_no = ptd.item_no
    LEFT JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id 
        AND pt.transaction_date BETWEEN ? AND ?
    GROUP BY iwa.itemid
),
MonthlyReturned AS (
    -- All returns for the month aggregated by itemid
    SELECT 
        iwa.itemid,
        COALESCE(SUM(r.quantity_returned), 0) as returned_qty,
        COUNT(DISTINCT r.return_id) as return_count,
        GROUP_CONCAT(DISTINCT r.return_reason ORDER BY r.date_returned SEPARATOR '; ') as return_reasons
    FROM ItemsWithActivity iwa
    LEFT JOIN return_to_supplier r ON iwa.itemid = r.itemid 
        AND r.date_returned BETWEEN ? AND ?
    GROUP BY iwa.itemid
)
SELECT 
    iwa.itemid,
    iwa.generaldescription as item_name,
    iwa.unitmeasure,
    COALESCE(stock_info.item_no, 0) as item_no,
    COALESCE(stock_info.cris_no, '') as cris_no,
    COALESCE(stock_info.avg_unit_cost, 0) as unit_cost,
    
    -- Beginning balance data
    COALESCE(bb.beginning_qty, 0) as beginning_qty,
    
    -- Monthly received data with details
    COALESCE(mr.received_qty, 0) as received_qty,
    COALESCE(mr.delivery_count, 0) as delivery_count,
    mr.delivery_dates,
    mr.suppliers,
    
    -- Monthly dispensed data with analytics
    COALESCE(md_disp.dispensed_qty, 0) as dispensed_qty,
    COALESCE(md_disp.transaction_count, 0) as transaction_count,
    COALESCE(md_disp.active_days, 0) as active_days,
    md_disp.first_dispensed,
    md_disp.last_dispensed,
    
    -- Monthly returned data
    COALESCE(mr_ret.returned_qty, 0) as returned_qty,
    COALESCE(mr_ret.return_count, 0) as return_count,
    mr_ret.return_reasons,
    
    -- Calculated ending balance
    (COALESCE(bb.beginning_qty, 0) + 
     COALESCE(mr.received_qty, 0) - 
     COALESCE(md_disp.dispensed_qty, 0) - 
     COALESCE(mr_ret.returned_qty, 0)) as ending_qty

FROM ItemsWithActivity iwa
LEFT JOIN (
    SELECT 
        itemid,
        MIN(item_no) as item_no,
        GROUP_CONCAT(DISTINCT cris_no SEPARATOR ', ') as cris_no,
        AVG(unit_cost) as avg_unit_cost
    FROM pharmacy_stock_ledger 
    GROUP BY itemid
) stock_info ON iwa.itemid = stock_info.itemid
LEFT JOIN BeginningBalance bb ON iwa.itemid = bb.itemid
LEFT JOIN MonthlyReceived mr ON iwa.itemid = mr.itemid
LEFT JOIN MonthlyDispensed md_disp ON iwa.itemid = md_disp.itemid
LEFT JOIN MonthlyReturned mr_ret ON iwa.itemid = mr_ret.itemid
WHERE (bb.beginning_qty > 0 OR mr.received_qty > 0 OR md_disp.dispensed_qty > 0 OR mr_ret.returned_qty > 0)
ORDER BY stock_info.item_no ASC, iwa.generaldescription ASC
";

$stmt = $conn->prepare($sql);
$stmt->execute([
    $startDate, $endDate, // AllActiveItems - transaction date range check
    $startDate,         // BeginningBalance - psl.date_received < ?
    $startDate,         // BeginningBalance - pt.transaction_date < ?
    $startDate,         // BeginningBalance - r.date_returned < ?
    $startDate, $endDate, // MonthlyReceived - date range
    $startDate, $endDate, // MonthlyDispensed - date range
    $startDate, $endDate  // MonthlyReturned - date range
]);

$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>Total Results: " . count($results) . "</h3>";

// Check for duplicates by itemid
$itemIds = array_column($results, 'itemid');
$itemNames = array_column($results, 'item_name');
$duplicateIds = [];
$duplicateNames = [];

// Find duplicate itemids
$itemIdCounts = array_count_values($itemIds);
foreach ($itemIdCounts as $itemId => $count) {
    if ($count > 1) {
        $duplicateIds[] = $itemId;
    }
}

// Find duplicate names
$itemNameCounts = array_count_values($itemNames);
foreach ($itemNameCounts as $itemName => $count) {
    if ($count > 1) {
        $duplicateNames[] = $itemName;
    }
}

if (empty($duplicateIds) && empty($duplicateNames)) {
    echo "<div style='color: green; font-size: 18px;'>✅ <strong>NO DUPLICATES FOUND!</strong></div>";
    echo "<p>All items appear exactly once in the results.</p>";
} else {
    if (!empty($duplicateIds)) {
        echo "<div style='color: red; font-size: 18px;'>❌ <strong>DUPLICATE ITEM IDs FOUND!</strong></div>";
        echo "<p><strong>Duplicate Item IDs:</strong> " . implode(', ', $duplicateIds) . "</p>";
        
        echo "<h4>Duplicate Items by ID:</h4>";
        foreach ($duplicateIds as $dupId) {
            echo "<div style='border: 1px solid red; padding: 10px; margin: 5px;'>";
            echo "<strong>Item ID: $dupId</strong><br>";
            foreach ($results as $result) {
                if ($result['itemid'] == $dupId) {
                    echo "- {$result['item_name']} (Item No: {$result['item_no']})<br>";
                }
            }
            echo "</div>";
        }
    }
    
    if (!empty($duplicateNames)) {
        echo "<div style='color: orange; font-size: 18px;'>⚠️ <strong>DUPLICATE ITEM NAMES FOUND!</strong></div>";
        echo "<p><strong>Duplicate Item Names:</strong></p>";
        
        foreach ($duplicateNames as $dupName) {
            echo "<div style='border: 1px solid orange; padding: 10px; margin: 5px;'>";
            echo "<strong>Item Name: $dupName</strong><br>";
            foreach ($results as $result) {
                if ($result['item_name'] == $dupName) {
                    echo "- ID: {$result['itemid']}, Item No: {$result['item_no']}<br>";
                }
            }
            echo "</div>";
        }
    }
}

// Show last 5 items for inspection
echo "<h3>Last 5 Items in Results:</h3>";
$lastItems = array_slice($results, -5);
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Item ID</th><th>Item Name</th><th>Item No</th><th>Beginning Qty</th><th>Received Qty</th><th>Dispensed Qty</th></tr>";
foreach ($lastItems as $item) {
    echo "<tr>";
    echo "<td>{$item['itemid']}</td>";
    echo "<td>{$item['item_name']}</td>";
    echo "<td>{$item['item_no']}</td>";
    echo "<td>{$item['beginning_qty']}</td>";
    echo "<td>{$item['received_qty']}</td>";
    echo "<td>{$item['dispensed_qty']}</td>";
    echo "</tr>";
}
echo "</table>";

// Special check for Lidocaine items
echo "<h3>Special Check: Lidocaine Items</h3>";
$lidocaineSql = "SELECT i.itemid, i.generaldescription, psl.item_no, psl.cris_no, psl.unit_cost, psl.qty_received, psl.date_received
                 FROM items i
                 JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid
                 WHERE i.generaldescription LIKE '%Lidocaine%'
                 ORDER BY i.generaldescription, psl.item_no";

$lidocaineStmt = $conn->query($lidocaineSql);
$lidocaineResults = $lidocaineStmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Item ID</th><th>Description</th><th>Item No</th><th>CRIS No</th><th>Unit Cost</th><th>Qty Received</th><th>Date Received</th></tr>";
foreach ($lidocaineResults as $item) {
    echo "<tr>";
    echo "<td>{$item['itemid']}</td>";
    echo "<td>{$item['generaldescription']}</td>";
    echo "<td>{$item['item_no']}</td>";
    echo "<td>{$item['cris_no']}</td>";
    echo "<td>{$item['unit_cost']}</td>";
    echo "<td>{$item['qty_received']}</td>";
    echo "<td>{$item['date_received']}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<br><a href='mur.php'>← Back to MUR Report</a>";
?>
