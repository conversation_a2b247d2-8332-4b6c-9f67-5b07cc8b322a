<?php
require_once '../database.php';

// Get selected month and year or default to current month
$selectedMonth = isset($_GET['month']) ? $_GET['month'] : date('Y-m');
$startDate = $selectedMonth . '-01';
$endDate = date('Y-m-t', strtotime($startDate));

// Get previous month
$prevMonth = date('Y-m', strtotime($startDate . ' -1 month'));
$prevMonthStart = $prevMonth . '-01';
$prevMonthEnd = date('Y-m-t', strtotime($prevMonthStart));

$sql = "
SELECT 
    i.itemid,
    i.generaldescription AS item_name,
    i.unitmeasure,
    psl.item_no,
    psl.cris_no,
    psl.unit_cost,

    -- Beginning balance
    COALESCE(
        (SELECT JSON_OBJECT(
            'qty', 
                COALESCE(SUM(CASE 
                    WHEN psl2.date_received <= ? THEN psl2.qty_received 
                    ELSE 0 
                END), 0)
              - COALESCE(SUM(CASE 
                    WHEN t.transaction_date <= ? THEN ptd2.quantity 
                    ELSE 0 
                END), 0)
              - COALESCE(SUM(CASE 
                    WHEN r.date_returned <= ? THEN r.quantity_returned 
                    ELSE 0 
                END), 0),
            'unit_cost', psl.unit_cost
        )
        FROM items i2
        INNER JOIN pharmacy_stock_ledger psl2 ON i2.itemid = psl2.itemid
        LEFT JOIN pharmatransaction_details ptd2 ON psl2.item_no = ptd2.item_no
        LEFT JOIN pharmatransactions t ON ptd2.transaction_id = t.transaction_id
        LEFT JOIN return_to_supplier r ON i2.itemid = r.itemid
        WHERE i2.itemid = i.itemid
        GROUP BY i2.itemid), 
        JSON_OBJECT('qty', 0, 'unit_cost', 0)
    ) AS beginning_data,

    -- Received
    COALESCE(
        (SELECT JSON_OBJECT(
            'qty', SUM(qty_received),
            'unit_cost', psl.unit_cost
        )
        FROM pharmacy_stock_ledger psl2
        WHERE psl2.itemid = i.itemid 
        AND psl2.date_received BETWEEN ? AND ?
        GROUP BY psl2.itemid),
        JSON_OBJECT('qty', 0, 'unit_cost', 0)
    ) AS received_data,

    -- Dispensed
    COALESCE(
        (SELECT JSON_OBJECT(
            'qty', SUM(ptd2.quantity),
            'unit_cost', psl.unit_cost
        )
        FROM pharmatransactions t
        JOIN pharmatransaction_details ptd2 ON t.transaction_id = ptd2.transaction_id
        JOIN pharmacy_stock_ledger psl2 ON ptd2.item_no = psl2.item_no
        WHERE psl2.itemid = i.itemid 
        AND t.transaction_date BETWEEN ? AND ?
        GROUP BY psl2.itemid),
        JSON_OBJECT('qty', 0, 'unit_cost', 0)
    ) AS dispensed_data,

    -- Returned
    COALESCE(
        (SELECT JSON_OBJECT(
            'qty', SUM(r.quantity_returned),
            'unit_cost', psl.unit_cost
        )
        FROM return_to_supplier r
        JOIN pharmacy_stock_ledger psl2 ON r.itemid = psl2.itemid
        WHERE r.itemid = i.itemid 
        AND r.date_returned BETWEEN ? AND ?
        GROUP BY r.itemid),
        JSON_OBJECT('qty', 0, 'unit_cost', 0)
    ) AS returned_data

FROM items i
INNER JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid
INNER JOIN pharmatransaction_details ptd ON psl.item_no = ptd.item_no
WHERE psl.item_no IS NOT NULL
GROUP BY i.itemid, i.generaldescription, i.unitmeasure, psl.item_no, psl.cris_no, psl.unit_cost
ORDER BY psl.item_no ASC;
";

$stmt = $conn->prepare($sql);
try {
    $params = [
        $prevMonthEnd,
        $prevMonthEnd,
        $prevMonthEnd,
        $startDate,
        $endDate,
        $startDate,
        $endDate,
        $startDate,
        $endDate
    ];
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Convert JSON strings to arrays
    foreach ($results as &$row) {
        $row['beginning_data'] = json_decode($row['beginning_data'], true);
        $row['received_data'] = json_decode($row['received_data'], true);
        $row['dispensed_data'] = json_decode($row['dispensed_data'], true);
        $row['returned_data'] = json_decode($row['returned_data'], true);
    }
} catch (PDOException $e) {
    error_log("SQL Error: " . $e->getMessage());
    die("An error occurred while fetching data. Please check the error log.");
}

// Calculate ending balance and totals
foreach ($results as &$row) {
    // Get unit cost from the row data
    $unit_cost = $row['unit_cost'];

    // Calculate beginning balance
    $beginning_qty = $row['beginning_data']['qty'];
    $beginning_total = $beginning_qty * $unit_cost;

    // Calculate received
    $received_qty = $row['received_data']['qty'];
    $received_total = $received_qty * $unit_cost;

    // Calculate dispensed
    $dispensed_qty = $row['dispensed_data']['qty'];
    $dispensed_total = $dispensed_qty * $unit_cost;

    // Calculate returned
    $returned_qty = $row['returned_data']['qty'];
    $returned_total = $returned_qty * $unit_cost;

    // Calculate ending balance
    $ending_qty = $beginning_qty + $received_qty - $dispensed_qty - $returned_qty;
    $ending_total = $ending_qty * $unit_cost;

    // Store all calculations in balances array
    $row['balances'] = [
        'beginning' => ['qty' => $beginning_qty, 'unit_cost' => $unit_cost, 'total' => $beginning_total],
        'received' => ['qty' => $received_qty, 'unit_cost' => $unit_cost, 'total' => $received_total],
        'dispensed' => ['qty' => $dispensed_qty, 'unit_cost' => $unit_cost, 'total' => $dispensed_total],
        'returned' => ['qty' => $returned_qty, 'unit_cost' => $unit_cost, 'total' => $returned_total],
        'ending' => ['qty' => $ending_qty, 'unit_cost' => $unit_cost, 'total' => $ending_total]
    ];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Utilization Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-chart-line me-2"></i>Monthly Utilization Report</h4>
                <form method="GET" class="d-flex align-items-center">
                    <input type="month" class="form-control me-2" name="month" 
                           value="<?php echo $selectedMonth; ?>" onchange="this.form.submit()">
                    <a href="pharmacydashboard.php" class="btn btn-light">
                        <i class="fas fa-home"></i>
                    </a>
                    <a href="mur_report.php?month=<?php echo $selectedMonth; ?>" class="btn btn-light ms-2" target="_blank">
                        <i class="fas fa-print"></i>
                    </a>
                </form>
            </div>
             <?php
            foreach ($results as &$row) {
                // Safely decode or accept already-decoded data
                $beginning_data  = is_string($row['beginning_data'])  ? json_decode($row['beginning_data'], true)  : $row['beginning_data'];
                $received_data   = is_string($row['received_data'])   ? json_decode($row['received_data'], true)   : $row['received_data'];
                $dispensed_data  = is_string($row['dispensed_data'])  ? json_decode($row['dispensed_data'], true)  : $row['dispensed_data'];
                $returned_data   = is_string($row['returned_data'])   ? json_decode($row['returned_data'], true)   : $row['returned_data'];

                // Fallbacks
                $beginning_data  = is_array($beginning_data)  ? $beginning_data  : ['qty' => 0, 'unit_cost' => 0];
                $received_data   = is_array($received_data)   ? $received_data   : ['qty' => 0, 'unit_cost' => 0];
                $dispensed_data  = is_array($dispensed_data)  ? $dispensed_data  : ['qty' => 0, 'unit_cost' => 0];
                $returned_data   = is_array($returned_data)   ? $returned_data   : ['qty' => 0, 'unit_cost' => 0];

                // Initialize balances array
                $row['balances'] = [
                    'beginning' => ['qty' => $beginning_data['qty'], 'cost' => $beginning_data['unit_cost']],
                    'received'  => ['qty' => $received_data['qty'],  'cost' => $received_data['unit_cost']],
                    'dispensed' => ['qty' => $dispensed_data['qty'], 'cost' => $dispensed_data['unit_cost']],
                    'returned'  => ['qty' => $returned_data['qty'],  'cost' => $returned_data['unit_cost']],
                ];

                $groupKeys = ['beginning', 'received', 'dispensed', 'returned', 'ending'];

                foreach ($groupKeys as $group) {
                    $balance = $row['balances'][$group] ?? ['qty' => 0, 'cost' => 0, 'total' => 0];

                    // Decode JSON if still string (defensive)
                    if (is_string($balance)) {
                        $balance = json_decode($balance, true);
                    }

                    if (!is_array($balance)) {
                        $balance = ['qty' => 0, 'cost' => 0, 'total' => 0];
                    }

                    // 🔁 Recalculate ending
                    if ($group === 'ending') {
                        $beginning_qty = $row['balances']['beginning']['qty'] ?? 0;
                        $received_qty  = $row['balances']['received']['qty'] ?? 0;
                        $dispensed_qty = $row['balances']['dispensed']['qty'] ?? 0;
                        $returned_qty  = $row['balances']['returned']['qty'] ?? 0;

                        $ending_qty = $beginning_qty + $received_qty - $dispensed_qty - $returned_qty;
                        $ending_cost = $row['balances']['beginning']['cost'] ?? 0;
                        $ending_total = $ending_qty * $ending_cost;

                        $balance = [
                            'qty' => $ending_qty,
                            'cost' => $ending_cost,
                            'total' => $ending_total
                        ];
                    } else {
                        // 💡 Compute total for other groups
                        $balance['total'] = ($balance['qty'] ?? 0) * ($balance['cost'] ?? 0);
                    }

                    $row['balances'][$group] = $balance;
                }
            }
            unset($row);
            ?>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover table-striped table-bordered">
                        <thead class="bg-light">
                            <tr class="text-center">
                                <th class="align-middle" rowspan="2">Item No</th>
                                <th class="align-middle" rowspan="2" style="min-width: 200px;">Item Name (CRIS No.)</th>
                                <th class="align-middle" rowspan="2">Unit</th>
                                <th class="bg-info text-white" colspan="3">Beginning Balance</th>
                                <th class="bg-success text-white" colspan="3">Replenishments</th>
                                <th class="bg-warning text-white" colspan="3">Utilizations</th>
                                <th class="bg-danger text-white" colspan="3">Returns to Warehouse</th>
                                <th class="bg-primary text-white" colspan="3">Ending Balance</th>
                            </tr>
                            <tr class="text-center">
                                <?php foreach(['Beginning', 'Received', 'Dispensed', 'Returned', 'Ending'] as $group): ?>
                                    <th>Qty</th>
                                    <th>Unit Cost</th>
                                    <th>Total</th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $row): ?>
                            <tr>
                                <td class="text-center"><?php echo htmlspecialchars($row['item_no'] ?? ''); ?></td>
                                <td><?php echo htmlspecialchars($row['item_name'] . ' (' . ($row['cris_no'] ?? '') . ')'); ?></td>
                                <td class="text-center"><?php echo htmlspecialchars($row['unitmeasure'] ?? ''); ?></td>

                                <?php foreach(['beginning', 'received', 'dispensed', 'returned', 'ending'] as $type): ?>
                                    <td class="text-end"><?php echo number_format($row['balances'][$type]['qty'] ?? 0); ?></td>
                                    <td class="text-end">₱<?php echo number_format($row['balances'][$type]['cost'] ?? 0, 2); ?></td>
                                    <td class="text-end fw-bold">₱<?php echo number_format($row['balances'][$type]['total'] ?? 0, 2); ?></td>
                                <?php endforeach; ?>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
