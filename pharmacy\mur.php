<?php
require_once '../database.php';

// Get selected month and year or default to current month
$selectedMonth = isset($_GET['month']) ? $_GET['month'] : date('Y-m');
$startDate = $selectedMonth . '-01';
$endDate = date('Y-m-t', strtotime($startDate));

// Get previous month
$prevMonth = date('Y-m', strtotime($startDate . ' -1 month'));
$prevMonthStart = $prevMonth . '-01';
$prevMonthEnd = date('Y-m-t', strtotime($prevMonthStart));

// Enhanced SQL with proper aggregation and better performance
$sql = "
WITH MonthlyData AS (
    -- Get all items with their basic info
    SELECT DISTINCT
        i.itemid,
        i.generaldescription AS item_name,
        i.unitmeasure,
        MIN(psl.item_no) as item_no,
        GROUP_CONCAT(DISTINCT psl.cris_no SEPARATOR ', ') as cris_no,
        AVG(psl.unit_cost) as avg_unit_cost
    FROM items i
    INNER JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid
    GROUP BY i.itemid, i.generaldescription, i.unitmeasure
),
BeginningBalance AS (
    -- Calculate beginning balance (everything before start date)
    SELECT
        i.itemid,
        COALESCE(SUM(psl.qty_received), 0) as total_received,
        COALESCE(SUM(ptd.quantity), 0) as total_dispensed,
        COALESCE(SUM(r.quantity_returned), 0) as total_returned,
        (COALESCE(SUM(psl.qty_received), 0) -
         COALESCE(SUM(ptd.quantity), 0) -
         COALESCE(SUM(r.quantity_returned), 0)) as beginning_qty
    FROM items i
    LEFT JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid AND psl.date_received < ?
    LEFT JOIN pharmatransaction_details ptd ON psl.item_no = ptd.item_no
    LEFT JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id AND pt.transaction_date < ?
    LEFT JOIN return_to_supplier r ON i.itemid = r.itemid AND r.date_returned < ?
    GROUP BY i.itemid
),
MonthlyReceived AS (
    -- All deliveries/receipts for the month aggregated by itemid
    SELECT
        i.itemid,
        COALESCE(SUM(psl.qty_received), 0) as received_qty,
        COUNT(DISTINCT psl.date_received) as delivery_count,
        GROUP_CONCAT(DISTINCT DATE_FORMAT(psl.date_received, '%m/%d') ORDER BY psl.date_received SEPARATOR ', ') as delivery_dates,
        GROUP_CONCAT(DISTINCT psl.supplier ORDER BY psl.supplier SEPARATOR ', ') as suppliers
    FROM items i
    LEFT JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid
        AND psl.date_received BETWEEN ? AND ?
    GROUP BY i.itemid
),
MonthlyDispensed AS (
    -- All dispensing/utilization for the month aggregated by itemid
    SELECT
        i.itemid,
        COALESCE(SUM(ptd.quantity), 0) as dispensed_qty,
        COUNT(DISTINCT pt.transaction_id) as transaction_count,
        COUNT(DISTINCT DATE(pt.transaction_date)) as active_days,
        MIN(DATE(pt.transaction_date)) as first_dispensed,
        MAX(DATE(pt.transaction_date)) as last_dispensed
    FROM items i
    LEFT JOIN pharmacy_stock_ledger psl ON i.itemid = psl.itemid
    LEFT JOIN pharmatransaction_details ptd ON psl.item_no = ptd.item_no
    LEFT JOIN pharmatransactions pt ON ptd.transaction_id = pt.transaction_id
        AND pt.transaction_date BETWEEN ? AND ?
    GROUP BY i.itemid
),
MonthlyReturned AS (
    -- All returns for the month aggregated by itemid
    SELECT
        i.itemid,
        COALESCE(SUM(r.quantity_returned), 0) as returned_qty,
        COUNT(DISTINCT r.return_id) as return_count,
        GROUP_CONCAT(DISTINCT r.return_reason ORDER BY r.date_returned SEPARATOR '; ') as return_reasons
    FROM items i
    LEFT JOIN return_to_supplier r ON i.itemid = r.itemid
        AND r.date_returned BETWEEN ? AND ?
    GROUP BY i.itemid
)
SELECT
    md.itemid,
    md.item_name,
    md.unitmeasure,
    md.item_no,
    md.cris_no,
    md.avg_unit_cost as unit_cost,

    -- Beginning balance data
    COALESCE(bb.beginning_qty, 0) as beginning_qty,

    -- Monthly received data with details
    COALESCE(mr.received_qty, 0) as received_qty,
    COALESCE(mr.delivery_count, 0) as delivery_count,
    mr.delivery_dates,
    mr.suppliers,

    -- Monthly dispensed data with analytics
    COALESCE(md_disp.dispensed_qty, 0) as dispensed_qty,
    COALESCE(md_disp.transaction_count, 0) as transaction_count,
    COALESCE(md_disp.active_days, 0) as active_days,
    md_disp.first_dispensed,
    md_disp.last_dispensed,

    -- Monthly returned data
    COALESCE(mr_ret.returned_qty, 0) as returned_qty,
    COALESCE(mr_ret.return_count, 0) as return_count,
    mr_ret.return_reasons,

    -- Calculated ending balance
    (COALESCE(bb.beginning_qty, 0) +
     COALESCE(mr.received_qty, 0) -
     COALESCE(md_disp.dispensed_qty, 0) -
     COALESCE(mr_ret.returned_qty, 0)) as ending_qty

FROM MonthlyData md
LEFT JOIN BeginningBalance bb ON md.itemid = bb.itemid
LEFT JOIN MonthlyReceived mr ON md.itemid = mr.itemid
LEFT JOIN MonthlyDispensed md_disp ON md.itemid = md_disp.itemid
LEFT JOIN MonthlyReturned mr_ret ON md.itemid = mr_ret.itemid
WHERE (bb.beginning_qty > 0 OR mr.received_qty > 0 OR md_disp.dispensed_qty > 0 OR mr_ret.returned_qty > 0)
ORDER BY md.item_no ASC, md.item_name ASC
";

$stmt = $conn->prepare($sql);
try {
    $params = [
        $startDate,         // BeginningBalance - psl.date_received < ?
        $startDate,         // BeginningBalance - pt.transaction_date < ?
        $startDate,         // BeginningBalance - r.date_returned < ?
        $startDate, $endDate, // MonthlyReceived - date range
        $startDate, $endDate, // MonthlyDispensed - date range
        $startDate, $endDate  // MonthlyReturned - date range
    ];
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    error_log("SQL Error: " . $e->getMessage());
    die("An error occurred while fetching data. Please check the error log.");
}

// Calculate values and enhanced metrics
foreach ($results as &$row) {
    // Calculate monetary values
    $row['beginning_value'] = $row['beginning_qty'] * $row['unit_cost'];
    $row['received_value'] = $row['received_qty'] * $row['unit_cost'];
    $row['dispensed_value'] = $row['dispensed_qty'] * $row['unit_cost'];
    $row['returned_value'] = $row['returned_qty'] * $row['unit_cost'];
    $row['ending_value'] = $row['ending_qty'] * $row['unit_cost'];

    // Calculate utilization metrics
    $total_available = $row['beginning_qty'] + $row['received_qty'];
    $row['utilization_rate'] = $total_available > 0 ?
        round(($row['dispensed_qty'] / $total_available) * 100, 2) : 0;

    // Calculate turnover rate
    $row['turnover_rate'] = $row['received_qty'] > 0 ?
        round($row['dispensed_qty'] / $row['received_qty'], 2) : 0;

    // Days of supply calculation
    $days_in_month = date('t', strtotime($startDate));
    $daily_usage = $row['active_days'] > 0 ? $row['dispensed_qty'] / $row['active_days'] : 0;
    $row['days_supply'] = $daily_usage > 0 ? round($row['ending_qty'] / $daily_usage, 0) : 'N/A';

    // Stock status assessment
    if ($row['ending_qty'] <= 0) {
        $row['stock_status'] = 'Out of Stock';
        $row['status_class'] = 'danger';
    } elseif ($row['days_supply'] !== 'N/A' && $row['days_supply'] < 7) {
        $row['stock_status'] = 'Critical Low';
        $row['status_class'] = 'danger';
    } elseif ($row['days_supply'] !== 'N/A' && $row['days_supply'] < 14) {
        $row['stock_status'] = 'Low Stock';
        $row['status_class'] = 'warning';
    } else {
        $row['stock_status'] = 'Adequate';
        $row['status_class'] = 'success';
    }

    // Store all calculations in balances array for backward compatibility
    $row['balances'] = [
        'beginning' => ['qty' => $row['beginning_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['beginning_value']],
        'received' => ['qty' => $row['received_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['received_value']],
        'dispensed' => ['qty' => $row['dispensed_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['dispensed_value']],
        'returned' => ['qty' => $row['returned_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['returned_value']],
        'ending' => ['qty' => $row['ending_qty'], 'unit_cost' => $row['unit_cost'], 'total' => $row['ending_value']]
    ];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Utilization Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-chart-line me-2"></i>Monthly Utilization Report</h4>
                <form method="GET" class="d-flex align-items-center">
                    <input type="month" class="form-control me-2" name="month" 
                           value="<?php echo $selectedMonth; ?>" onchange="this.form.submit()">
                    <a href="pharmacydashboard.php" class="btn btn-light">
                        <i class="fas fa-home"></i>
                    </a>
                    <a href="mur_report.php?month=<?php echo $selectedMonth; ?>" class="btn btn-light ms-2" target="_blank">
                        <i class="fas fa-print"></i>
                    </a>
                </form>
            </div>
            <div class="card-body">
                <!-- Summary Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-pills fa-2x mb-2"></i>
                                <h4><?php echo count($results); ?></h4>
                                <small>Total Items</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-truck fa-2x mb-2"></i>
                                <h4><?php echo array_sum(array_column($results, 'delivery_count')); ?></h4>
                                <small>Total Deliveries</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-exchange-alt fa-2x mb-2"></i>
                                <h4><?php echo array_sum(array_column($results, 'transaction_count')); ?></h4>
                                <small>Total Transactions</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <h4><?php echo count(array_filter($results, function($r) { return $r['stock_status'] === 'Out of Stock' || $r['stock_status'] === 'Critical Low'; })); ?></h4>
                                <small>Critical Items</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Table -->
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th rowspan="2">Item Description</th>
                                <th rowspan="2">Unit</th>
                                <th rowspan="2">CRIS No.</th>
                                <th colspan="2" class="text-center">Beginning</th>
                                <th colspan="4" class="text-center">Received (Monthly)</th>
                                <th colspan="4" class="text-center">Dispensed (Monthly)</th>
                                <th colspan="2" class="text-center">Returned</th>
                                <th colspan="2" class="text-center">Ending</th>
                                <th rowspan="2">Stock Status</th>
                                <th rowspan="2">Days Supply</th>
                                <th rowspan="2">Utilization %</th>
                            </tr>
                            <tr>
                                <th>Qty</th>
                                <th>Value</th>
                                <th>Qty</th>
                                <th>Deliveries</th>
                                <th>Suppliers</th>
                                <th>Value</th>
                                <th>Qty</th>
                                <th>Transactions</th>
                                <th>Active Days</th>
                                <th>Value</th>
                                <th>Qty</th>
                                <th>Value</th>
                                <th>Qty</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $row): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['item_name']); ?></td>
                                <td><?php echo htmlspecialchars($row['unitmeasure']); ?></td>
                                <td><?php echo htmlspecialchars($row['cris_no']); ?></td>

                                <!-- Beginning Balance -->
                                <td class="text-end"><?php echo number_format($row['beginning_qty']); ?></td>
                                <td class="text-end">₱<?php echo number_format($row['beginning_value'], 2); ?></td>

                                <!-- Received (Monthly Aggregated) -->
                                <td class="text-end fw-bold text-success"><?php echo number_format($row['received_qty']); ?></td>
                                <td class="text-center">
                                    <span class="badge bg-info"><?php echo $row['delivery_count']; ?></span>
                                    <?php if ($row['delivery_dates']): ?>
                                    <br><small class="text-muted"><?php echo $row['delivery_dates']; ?></small>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <?php if ($row['suppliers']): ?>
                                    <small class="text-muted"><?php echo htmlspecialchars(substr($row['suppliers'], 0, 30)) . (strlen($row['suppliers']) > 30 ? '...' : ''); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td class="text-end">₱<?php echo number_format($row['received_value'], 2); ?></td>

                                <!-- Dispensed (Monthly Aggregated) -->
                                <td class="text-end fw-bold text-warning"><?php echo number_format($row['dispensed_qty']); ?></td>
                                <td class="text-center">
                                    <span class="badge bg-warning"><?php echo $row['transaction_count']; ?></span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-secondary"><?php echo $row['active_days']; ?> days</span>
                                    <?php if ($row['first_dispensed'] && $row['last_dispensed']): ?>
                                    <br><small class="text-muted"><?php echo date('m/d', strtotime($row['first_dispensed'])); ?> - <?php echo date('m/d', strtotime($row['last_dispensed'])); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td class="text-end">₱<?php echo number_format($row['dispensed_value'], 2); ?></td>

                                <!-- Returned -->
                                <td class="text-end text-danger"><?php echo number_format($row['returned_qty']); ?></td>
                                <td class="text-end">₱<?php echo number_format($row['returned_value'], 2); ?></td>

                                <!-- Ending Balance -->
                                <td class="text-end fw-bold"><?php echo number_format($row['ending_qty']); ?></td>
                                <td class="text-end fw-bold">₱<?php echo number_format($row['ending_value'], 2); ?></td>

                                <!-- Stock Status -->
                                <td class="text-center">
                                    <span class="badge bg-<?php echo $row['status_class']; ?>"><?php echo $row['stock_status']; ?></span>
                                </td>

                                <!-- Days Supply -->
                                <td class="text-center">
                                    <?php if ($row['days_supply'] !== 'N/A'): ?>
                                    <span class="badge <?php echo $row['days_supply'] < 7 ? 'bg-danger' : ($row['days_supply'] < 14 ? 'bg-warning' : 'bg-success'); ?>">
                                        <?php echo $row['days_supply']; ?> days
                                    </span>
                                    <?php else: ?>
                                    <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                </td>

                                <!-- Utilization Rate -->
                                <td class="text-center">
                                    <span class="badge <?php echo $row['utilization_rate'] > 80 ? 'bg-success' : ($row['utilization_rate'] > 50 ? 'bg-warning' : 'bg-danger'); ?>">
                                        <?php echo $row['utilization_rate']; ?>%
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Summary Totals -->
                <div class="card-footer bg-light">
                    <div class="row">
                        <div class="col-md-12">
                            <h5 class="mb-3"><i class="fas fa-calculator me-2"></i>Monthly Summary</h5>
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h6 class="text-info">Beginning Value</h6>
                                        <h4>₱<?php echo number_format(array_sum(array_column($results, 'beginning_value')), 2); ?></h4>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h6 class="text-success">Received Value</h6>
                                        <h4>₱<?php echo number_format(array_sum(array_column($results, 'received_value')), 2); ?></h4>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h6 class="text-warning">Dispensed Value</h6>
                                        <h4>₱<?php echo number_format(array_sum(array_column($results, 'dispensed_value')), 2); ?></h4>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h6 class="text-danger">Returned Value</h6>
                                        <h4>₱<?php echo number_format(array_sum(array_column($results, 'returned_value')), 2); ?></h4>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h6 class="text-primary">Ending Value</h6>
                                        <h4>₱<?php echo number_format(array_sum(array_column($results, 'ending_value')), 2); ?></h4>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <h6 class="text-secondary">Avg Utilization</h6>
                                        <h4><?php echo number_format(array_sum(array_column($results, 'utilization_rate')) / count($results), 1); ?>%</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
