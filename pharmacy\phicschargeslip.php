<?php
require_once '../database.php';
require_once '../fpdf/fpdf.php';

// Get transaction ID from URL
$transactionId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get transaction details
$sql = "SELECT t.*, p.patientname, p.patient_type 
        FROM pharmatransactions t 
        JOIN patient p ON t.patientid = p.patientid 
        WHERE t.transaction_id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$transactionId]);
$transaction = $stmt->fetch();

// Get transaction items with category
$sql = "SELECT d.*, i.generaldescription, i.unitmeasure, i.item_type 
        FROM pharmatransaction_details d
        JOIN pharmacy_stock_ledger psl ON d.item_no=psl.item_no 
        JOIN items i ON psl.itemid = i.itemid 
        WHERE d.transaction_id = ?
        ORDER BY i.item_type, i.generaldescription";
$stmt = $conn->prepare($sql);
$stmt->execute([$transactionId]);
$transactionItems = $stmt->fetchAll();

// Group items by category
$groupedItems = [];
foreach ($transactionItems as $item) {
    $category = $item['item_type'] ?: 'Other';
    if (!isset($groupedItems[$category])) {
        $groupedItems[$category] = [];
    }
    $groupedItems[$category][] = $item;
}

// Create PDF (A4 size)
$pdf = new FPDF('P', 'mm', 'A4');
$pdf->AddPage();
$pdf->SetMargins(10, 10, 10);

// Add logos (smaller and closer to top)
$pdf->Image('../images/pgns.png', 35, 8, 20);
$pdf->Image('../images/bdh.png', 140, 8, 20);

// Header section with compact styling
$pdf->SetFont('Times', '', 10);
$pdf->Cell(0, 4, 'Republic of the Philippines', 0, 1, 'C');
$pdf->Cell(0, 4, 'Province of Northern Samar', 0, 1, 'C');
$pdf->Cell(0, 4, 'Provincial Health Office', 0, 1, 'C');
$pdf->SetFont('Times', 'B', 14);
$pdf->Cell(0, 6, 'BIRI DISTRICT HOSPITAL', 0, 1, 'C');
$pdf->SetFont('Times', 'I', 9);
$pdf->Cell(0, 4, 'Biri Northern Samar', 0, 1, 'C');

// Receipt title
$pdf->Ln(2);
$pdf->SetFillColor(28, 58, 95);
$pdf->SetTextColor(255);
$pdf->SetFont('Arial', 'B', 12);
$pdf->Cell(0, 7, 'PHARMACY CHARGE SLIP', 0, 1, 'C', true);
$pdf->SetTextColor(0);

// Transaction details with compact layout
$pdf->SetFont('Arial', '', 9);
$pdf->Ln(2);

// Two-column layout for transaction details
$pdf->Cell(40, 4, 'Transaction #:', 0);
$pdf->Cell(65, 4, $transaction['transaction_reference'], 0, 0);
$pdf->Cell(20, 4, 'Date:', 0);
$pdf->Cell(0, 4, date('F d, Y', strtotime($transaction['transaction_date'])), 0, 1);

$pdf->Cell(40, 4, 'Patient:', 0);
$pdf->Cell(65, 4, $transaction['patientname'], 0, 0);
$pdf->Cell(20, 4, 'Ward:', 0);
$pdf->Cell(0, 4, $transaction['ward'], 0, 1);

$pdf->Cell(40, 4, 'Patient Type:', 0);
$pdf->Cell(0, 4, $transaction['patient_type'], 0, 1);

if ($transaction['patient_type'] == 'Senior' || $transaction['patient_type'] == 'PWD') {
    $pdf->SetFont('Arial', 'I', 8);
    $pdf->Cell(0, 4, 'Note: Total amount indicated is discounted by 20% as per law requires.', 0, 1);
}

// Compact separator
$pdf->Ln(2);
$pdf->SetDrawColor(28, 58, 95);
$pdf->SetLineWidth(0.3);
$pdf->Line($pdf->GetX(), $pdf->GetY(), $pdf->GetX() + 190, $pdf->GetY());
$pdf->Ln(2);

// Items header
$pdf->SetFillColor(28, 58, 95);
$pdf->SetTextColor(255);
$pdf->SetFont('Arial', 'B', 9);
$pdf->Cell(85, 6, 'Item Description', 1, 0, 'C', true);
$pdf->Cell(20, 6, 'Qty', 1, 0, 'C', true);
$pdf->Cell(40, 6, 'Unit Price', 1, 0, 'C', true);
$pdf->Cell(45, 6, 'Amount', 1, 0, 'C', true);
$pdf->Ln();
$pdf->SetTextColor(0);

// Items content
$pdf->SetFont('Arial', '', 8);
$total = 0;
$alternate = false;

foreach ($groupedItems as $category => $items) {
    $pdf->SetFillColor(240, 240, 240);
    $pdf->SetFont('Arial', 'B', 9);
    $pdf->Cell(0, 5, strtoupper($category), 0, 1, 'L', true);
    $pdf->SetFont('Arial', '', 8);
    
    foreach ($items as $item) {
        $alternate = !$alternate;
        $pdf->SetFillColor($alternate ? 255 : 248, $alternate ? 255 : 248, $alternate ? 255 : 248);
        
        $pdf->Cell(85, 5, $item['generaldescription'], 1, 0, 'L', true);
        $pdf->Cell(20, 5, $item['quantity'], 1, 0, 'C', true);
        $pdf->Cell(40, 5, number_format($item['unit_price'], 2), 1, 0, 'R', true);
        $pdf->Cell(45, 5, number_format($item['subtotal'], 2), 1, 0, 'R', true);
        $pdf->Ln();
        
        $total += $item['subtotal'];
    }
    $pdf->Ln(1);
}

// Total amount
$pdf->Ln(2);
$pdf->SetFillColor(28, 58, 95);
$pdf->SetTextColor(255);
$pdf->SetFont('Arial', 'B', 9);
$pdf->Cell(145, 6, 'Total Amount:', 0, 0, 'R', true);
$pdf->Cell(45, 6, 'PHP ' . number_format($total, 2), 0, 1, 'R', true);
$pdf->SetTextColor(0);

// Signature section
$pdf->Ln(5);
$pdf->SetFont('Arial', 'B', 9);
$pdf->Cell(95, 4, 'Prepared by:', 0, 0, 'L');
$pdf->Cell(95, 4, 'Noted by:', 0, 1, 'L');
$pdf->Ln(8);

$pharmacist = isset($_GET['pharmacist']) ? $_GET['pharmacist'] : 'RONNIE B. CELIS, RPh';

$pdf->Cell(95, 4, $pharmacist, 0, 0, 'C');
$pdf->Cell(95, 4, 'RONNIE B. CELIS, RPh', 0, 1, 'C');

$pdf->Line(25, $pdf->GetY() + 2, 100, $pdf->GetY() + 2);
$pdf->Line(120, $pdf->GetY() + 2, 195, $pdf->GetY() + 2);

$pdf->Ln(3);
$pdf->SetFont('Arial', 'I', 8);
$pdf->Cell(95, 4, 'Pharmacist', 0, 0, 'C');
$pdf->Cell(95, 4, 'Pharmacist II', 0, 1, 'C');

$pdf->Output();
